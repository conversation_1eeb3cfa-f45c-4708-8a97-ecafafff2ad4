import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
from gui.components.login import LoginFrame
from gui.components.devices import DevicesFrame
from gui.components.transactions import TransactionsFrame
from gui.components.logs import LogsFrame
from gui.utils.logger import log_manager
from gui.utils.thread_manager import thread_manager

try:
    from gui.utils._version import VERSION
except ImportError:
    VERSION = "開發版本"

class BankLoginApp(ttk.Window):
    def __init__(self):
        super().__init__(
            title=f"銀行登入管理系統 {VERSION}",
            themename="darkly",
            size=(1200, 800),
            position=(100, 50),
            minsize=(800, 600),
            resizable=(True, True)
        )
        
        # 應用程序狀態
        self.current_token = None
        
        # 初始化界面
        self.setup_ui()
        
        # 註冊關閉事件
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        log_manager.info(f"應用程序啟動 (版本 {VERSION})")
        
    def setup_ui(self):
        """設置界面"""
        # 主容器
        self.main_container = ttk.Frame(self)
        self.main_container.pack(fill=BOTH, expand=True)
        
        # 顯示登入界面
        self.show_login_interface()
        
    def show_login_interface(self):
        """顯示登入界面"""
        # 清空主容器
        for widget in self.main_container.winfo_children():
            widget.destroy()
            
        # 創建登入框架
        login_frame = LoginFrame(self.main_container)
        login_frame.pack(fill=BOTH, expand=True)
        
        log_manager.info("顯示登入界面")
        
    def show_main_interface(self):
        """顯示主界面"""
        try:
            # 清空主容器
            for widget in self.main_container.winfo_children():
                widget.destroy()
                
            # 創建主界面
            self.notebook = ttk.Notebook(self.main_container)
            self.notebook.pack(fill=BOTH, expand=True, padx=10, pady=5)
            
            # 設備管理頁面
            self.devices_frame = DevicesFrame(self.notebook, self)
            self.notebook.add(self.devices_frame, text="設備管理")
            
            # 交易記錄頁面
            self.transactions_frame = TransactionsFrame(self.notebook, self)
            self.notebook.add(self.transactions_frame, text="交易記錄")
            
            # 日誌頁面
            self.logs_frame = LogsFrame(self.main_container)
            self.logs_frame.pack(fill=X, padx=10, pady=5)
            
            # 記錄登入成功
            self.logs_frame.add_log("登入成功")
            log_manager.info("正在加載設備列表...")
            
            # 顯示主界面
            log_manager.info("顯示主界面")
            
            # 刷新設備列表
            self.devices_frame.refresh_devices()
            
        except Exception as e:
            log_manager.error(f"顯示主界面時發生錯誤: {str(e)}")
            Messagebox.show_error("顯示主界面時發生錯誤", "錯誤")
            self.show_login_interface()
            
    def on_login_success(self, token):
        """登入成功的回調"""
        try:
            self.current_token = token
            self.show_main_interface()
        except Exception as e:
            log_manager.error(f"處理登入成功時發生錯誤: {str(e)}")
            Messagebox.show_error("處理登入成功時發生錯誤", "錯誤")
            self.show_login_interface()
            
    def on_closing(self):
        """關閉應用程序"""
        try:
            # 停止所有線程
            thread_manager.stop_all_threads()
            
            # 關閉窗口
            self.quit()
            
        except Exception as e:
            log_manager.error(f"關閉應用程序時發生錯誤: {str(e)}")
            self.quit()
