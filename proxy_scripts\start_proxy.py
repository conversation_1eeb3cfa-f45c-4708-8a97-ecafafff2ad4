"""
啟動 mitmproxy 的腳本
"""
import os
import sys
import subprocess
import socket
import psutil # <--- 新增導入 psutil
import errno
from subprocess import PIPE
from pathlib import Path

# 添加父目錄到 sys.path
if getattr(sys, 'frozen', False):
    # 如果是打包後的執行檔
    base_path = os.path.dirname(sys.executable)
else:
    # 如果是開發環境
    base_path = os.path.dirname(os.path.dirname(__file__))

sys.path.append(base_path)

import shutil # <--- 新增導入 shutil
from gui.utils.logger import log_manager

# --- 新增：鎖定文件相關設定 ---
# --- 判斷應用程式資料（例如鎖定檔案）的基礎路徑 ---
if getattr(sys, 'frozen', False):
    # 如果應用程式是打包後執行的（例如 PyInstaller）
    app_base_path = Path(sys.executable).parent.absolute()
    # 在日誌系統可能尚未準備好時，使用 print 進行初始調試
    # print(f"調試：應用程式為打包模式，基礎路徑設為: {app_base_path}")
else:
    # 如果是從原始碼執行
    # 假設此腳本位於 proxy_scripts 中，且我們希望 locks 目錄位於專案根目錄
    app_base_path = Path(__file__).parent.parent.absolute()
    # print(f"調試：應用程式為開發模式，基礎路徑設為: {app_base_path}")

# --- 根據判斷出的基礎路徑定義 LOCK_DIR ---
LOCK_DIR = app_base_path / "locks"
try:
    LOCK_DIR.mkdir(exist_ok=True) # 確保鎖定目錄存在
    # print(f"調試：確保鎖定目錄存在: {LOCK_DIR}")
except OSError as e:
    # 在日誌系統可能尚未準備好時，使用 print
    print(f"錯誤：無法創建鎖定目錄 {LOCK_DIR}: {e}")
    # 如果鎖定目錄至關重要，考慮在此處退出或拋出異常
    sys.exit(f"錯誤：無法創建必要的鎖定目錄 {LOCK_DIR}")

# --- 鎖定檔案相關函數 ---
def get_lock_file_path(port):
    """獲取指定端口的鎖定文件絕對路徑"""
    # LOCK_DIR 現在是一個絕對路徑
    return LOCK_DIR / f"mitmproxy_port_{port}.lock"

def is_pid_running(pid):
    """檢查給定的 PID 是否正在運行"""
    if pid is None:
        return False
    try:
        return psutil.pid_exists(pid)
    except Exception as e:
        # 處理可能的權限問題或其他錯誤
        log_manager.warning(f"檢查 PID {pid} 時出錯: {e}")
        return False # 假設無法檢查的 PID 是無效的

def kill_all_mitmdump(device_id="system"): # 改名並設預設 device_id
    """強制關閉所有已存在的 mitmdump 進程"""
    log_manager.info("嘗試強制關閉所有 mitmdump.exe 進程...", device=device_id)
    try:
        # 使用 taskkill 命令強制結束 mitmdump 進程
        result = subprocess.run(['taskkill', '/F', '/IM', 'mitmdump.exe'],
                                capture_output=True, text=True, check=False)
        if "成功" in result.stdout or "SUCCESS" in result.stdout:
             log_manager.info("已強制關閉現有的 mitmdump 進程。", device=device_id)
        elif "找不到處理程序" in result.stderr or "not found" in result.stderr:
             log_manager.info("沒有找到正在運行的 mitmdump 進程。", device=device_id)
        else:
             log_manager.warning(f"嘗試關閉 mitmdump 進程時遇到問題: stdout={result.stdout}, stderr={result.stderr}", device=device_id)

        import time
        time.sleep(1)  # 等待進程完全關閉
    except FileNotFoundError:
         log_manager.warning("找不到 taskkill 命令，無法自動關閉 mitmdump 進程。請確保 taskkill 在系統 PATH 中。", device=device_id)
    except Exception as e:
        log_manager.error(f"關閉 mitmdump 進程時發生錯誤: {str(e)}", device=device_id)

def cleanup_all_locks(device_id="system"):
    """刪除所有 mitmproxy 鎖定文件"""
    log_manager.info(f"開始清理所有 mitmproxy 鎖定文件於目錄: {LOCK_DIR}", device=device_id)
    deleted_count = 0
    errors = 0
    if LOCK_DIR.exists():
        for item in LOCK_DIR.iterdir():
            if item.is_file() and item.name.startswith("mitmproxy_port_") and item.name.endswith(".lock"):
                try:
                    item.unlink()
                    log_manager.debug(f"已刪除鎖定文件: {item.name}", device=device_id)
                    deleted_count += 1
                except Exception as e:
                    log_manager.error(f"刪除鎖定文件 {item.name} 時發生錯誤: {e}", device=device_id)
                    errors += 1
        if deleted_count > 0:
             log_manager.info(f"完成鎖定文件清理，共刪除 {deleted_count} 個文件。", device=device_id)
        else:
             log_manager.info("未找到需要清理的鎖定文件。", device=device_id)
        if errors > 0:
             log_manager.warning(f"清理鎖定文件過程中發生 {errors} 個錯誤。", device=device_id)
    else:
        log_manager.info("鎖定文件目錄不存在，無需清理。", device=device_id)

def port_is_available(port):
    """檢查端口是否可用 (未被綁定)"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            # 嘗試綁定到 127.0.0.1
            s.bind(('127.0.0.1', port))
            return True # 綁定成功表示端口可用
        except OSError as e:
            # 如果錯誤是地址已在使用，則端口不可用
            if e.errno == errno.WSAEADDRINUSE or e.errno == errno.EADDRINUSE:
                return False
            # 其他錯誤則重新拋出
            raise

# --- kill_existing_mitmdump 函數不再需要，因為我們只清理無效的鎖定文件 ---

# --- 恢復並修正 find_available_port_with_lock_check 函數 ---
def find_available_port_with_lock_check(start_port=8081, max_attempts=100, device_id=None):
    """尋找一個可用的且沒有有效鎖定的端口 (修正邏輯)"""
    log_manager.debug(f"開始尋找可用端口 (起始: {start_port})...", device=device_id)
    for port in range(start_port, start_port + max_attempts):
        lock_file = get_lock_file_path(port)
        port_effectively_locked = False

        # 優先檢查鎖定文件是否存在且有效
        if lock_file.exists():
            try:
                with open(lock_file, 'r') as f:
                    pid_str = f.read().strip()
                    if pid_str:
                        pid = int(pid_str)
                        if is_pid_running(pid):
                            # 鎖定文件有效，此端口不可用
                            log_manager.debug(f"端口 {port} 被有效鎖定 (PID: {pid})，跳過。", device=device_id)
                            port_effectively_locked = True
                        else:
                            # 發現無效鎖定文件 (PID 不存在)，嘗試刪除
                            log_manager.warning(f"發現端口 {port} 的無效鎖定文件 (PID: {pid})，嘗試刪除...", device=device_id)
                            try:
                                lock_file.unlink()
                            except OSError as e:
                                log_manager.error(f"刪除無效鎖定文件 {lock_file.name} 失敗: {e}，視為端口仍被鎖定。", device=device_id)
                                port_effectively_locked = True # 刪除失敗，保守處理
                    else:
                        # 空鎖定文件，嘗試刪除
                        log_manager.warning(f"發現端口 {port} 的空鎖定文件，嘗試刪除...", device=device_id)
                        try:
                            lock_file.unlink()
                        except OSError as e:
                            log_manager.error(f"刪除空鎖定文件 {lock_file.name} 失敗: {e}，視為端口仍被鎖定。", device=device_id)
                            port_effectively_locked = True # 刪除失敗，保守處理
            except Exception as e:
                 log_manager.warning(f"處理鎖定文件 {lock_file.name} 時出錯: {e}，暫時跳過端口 {port}。", device=device_id)
                 port_effectively_locked = True # 出錯時保守處理

        # 如果端口已被有效鎖定，則直接跳到下一個端口
        if port_effectively_locked:
            continue

        # 端口未被有效鎖定，現在檢查端口是否實際可用 (未被綁定)
        if port_is_available(port):
            log_manager.info(f"找到可用且未被有效鎖定的端口: {port}", device=device_id)
            return port
        else:
            # 端口雖然沒有有效鎖定文件，但仍然被佔用 (可能是其他程序)
            log_manager.debug(f"端口 {port} 當前已被綁定 (但無有效鎖定文件)，跳過。", device=device_id)

    log_manager.error(f"在範圍 {start_port}-{start_port + max_attempts - 1} 內找不到可用且未被有效鎖定的端口。", device=device_id)
    return None
# --- find_available_port_with_lock_check 定義結束 ---

def find_mitmdump():
    """尋找 mitmdump 執行檔"""
    # 首先檢查打包後的依賴目錄
    if getattr(sys, 'frozen', False):
        # 打包後的執行檔環境
        bundled_path = os.path.join(os.path.dirname(sys.executable), 'dependencies', 'mitmproxy', 'mitmdump.exe')
    else:
        # 開發環境
        bundled_path = os.path.join(base_path, 'dependencies', 'mitmproxy', 'mitmdump.exe')

    if os.path.exists(bundled_path):
        return bundled_path

    # 如果打包的版本不存在，檢查系統安裝的版本
    # Windows 特定路徑
    possible_paths = [
        r"C:\Program Files\mitmproxy\bin\mitmdump.exe",
        r"C:\Program Files\mitmproxy\mitmdump.exe",
        r"C:\Program Files (x86)\mitmproxy\mitmdump.exe",
        os.path.expanduser("~\\AppData\\Local\\Programs\\mitmproxy\\mitmdump.exe")
    ]

    # 檢查可能的路徑
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 如果找不到，返回 None
    return None

def start_mitmproxy(device_id=None, upstream_proxy=None):
    """
    啟動 mitmproxy，使用動態端口，創建鎖定文件，並可選擇性地設定上游代理
    (假設清理工作已在應用啟動時完成)
    :param device_id: 設備 ID，用於日誌記錄
    :param upstream_proxy: 上游代理設定 (字典格式，包含 'server', 'username', 'password')
    :return: (subprocess.Popen object, port number, pid) or (None, None, None) if failed
    """
    # 1. 尋找可用且未被有效鎖定的端口
    listen_port = find_available_port_with_lock_check(device_id=device_id) # 使用修正後的函數

    # (原步驟 1 的 cleanup_stale_locks 已移除)
    if listen_port is None:
        log_manager.error("找不到可用的端口來啟動 mitmproxy", device=device_id)
        return None, None, None
    log_manager.info(f"為設備 {device_id} 分配的 mitmproxy 端口: {listen_port}", device=device_id)

    # 獲取腳本所在目錄
    script_dir = Path(__file__).parent.absolute()
    
    # mitmproxy 腳本路徑
    script_path = script_dir / "rakuten_modify.py"
    
    # 尋找系統安裝的 mitmdump
    mitmdump_path = find_mitmdump()
    log_manager.info(f"使用系統安裝的 mitmdump: {mitmdump_path}", device=device_id)
    
    if not mitmdump_path:
        log_manager.error("找不到系統安裝的 mitmdump，請確認已經安裝 mitmproxy", device=device_id)
        return None, None, None
    
    # 啟動 mitmproxy
    cmd = [
        mitmdump_path,
        "-s", str(script_path), 
        "--listen-port", str(listen_port), # 使用動態端口
        "--set", "block_global=false",
        "--ssl-insecure",  # 允許不安全的 SSL
        "--quiet",
        "--flow-detail", "0",
    ]

    # 處理上游代理設定
    if upstream_proxy and isinstance(upstream_proxy, dict) and 'server' in upstream_proxy:
        upstream_server = upstream_proxy['server']
        cmd.extend(["--mode", f"upstream:{upstream_server}"])
        log_manager.info(f"mitmproxy 將使用上游代理: {upstream_server}", device=device_id)

        # 檢查是否有身份驗證資訊
        if 'username' in upstream_proxy and 'password' in upstream_proxy:
            upstream_auth = f"{upstream_proxy['username']}:{upstream_proxy['password']}"
            cmd.extend(["--upstream-auth", upstream_auth])
            log_manager.info(f"mitmproxy 使用上游代理身份驗證 (使用者: {upstream_proxy['username'][:5]}...)", device=device_id)
    else:
        log_manager.info("mitmproxy 未設定上游代理", device=device_id)
    
    try:
        # 先測試 mitmdump 是否可以正常執行
        test_process = subprocess.run(
            [mitmdump_path, "--version"],
            stdout=PIPE,
            stderr=PIPE,
            text=True
        )
        if test_process.returncode != 0:
            log_manager.error(f"mitmdump 測試失敗: {test_process.stderr}", device=device_id)
            return None, None, None
            
        log_manager.info(f"mitmdump 版本: {test_process.stdout.strip()}", device=device_id)
        
        # 啟動主要進程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            creationflags=subprocess.CREATE_NO_WINDOW  # 避免在 Windows 上顯示命令提示符視窗
        )
        
        # 等待一下確認進程是否正常啟動
        import time
        time.sleep(2)
        
        if process.poll() is not None:
            # 進程已經結束，檢查錯誤輸出
            _, stderr = process.communicate()
            log_manager.error(f"mitmdump 啟動失敗: {stderr}", device=device_id)
            return None, None, None
            
        # 啟動一個線程來讀取輸出
        import threading
        def log_output(pipe, log_func):
            for line in pipe:
                log_func(line.strip(), device=device_id)
                
        threading.Thread(target=log_output, args=(process.stdout, log_manager.debug), daemon=True).start()
        threading.Thread(target=log_output, args=(process.stderr, log_manager.error), daemon=True).start()
            
        # 4. 創建鎖定文件並寫入 PID
        pid = process.pid
        lock_file = get_lock_file_path(listen_port)
        try:
            with open(lock_file, 'w') as f:
                f.write(str(pid))
            log_manager.info(f"創建鎖定文件 {lock_file.name} (PID: {pid})", device=device_id)
        except Exception as e:
            log_manager.error(f"創建鎖定文件 {lock_file.name} 失敗: {e}。mitmproxy 可能無法被正確管理。", device=device_id)
            # 即使創建鎖定失敗，仍然嘗試返回進程，但可能導致後續管理問題
            process.terminate() # 創建鎖定失敗，終止進程避免問題
            return None, None, None

        log_manager.info(f"mitmproxy (PID: {pid}) 已成功啟動於端口 {listen_port}", device=device_id)
        return process, listen_port, pid # 返回進程、端口號和 PID
        
    except Exception as e:
        log_manager.error(f"啟動 mitmproxy 時發生錯誤: {str(e)}", device=device_id)
        return None, None, None

if __name__ == "__main__":
    proxy_process, port, pid = start_mitmproxy() # 接收返回的端口號和 PID
    if proxy_process:
        try:
            proxy_process.wait()
        except KeyboardInterrupt:
            proxy_process.terminate()
            log_manager.info("mitmproxy 已停止")
