"""
第一銀行操作模組 (Playwright 版本)
實作第一銀行特定的自動化操作
"""

import time
import json
import pytz
from datetime import datetime
from playwright.sync_api import expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class FirstBank(BankBase):
    """第一銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        # 設定 is_mobile=False 使用電腦版介面
        super().__init__(
            bank_code="007",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=False,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_update_time = 0  # 上次更新的時間（包含 cookies 更新和重新計時）
        self.update_interval = 60  # 更新間隔（秒）
        self.last_refresh_date = None  # 上次重新整理的日期
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 獲取 frame1 的內容
            frame = self.page.locator("#frame1").content_frame
            if not frame:
                log_manager.debug("找不到 frame1", device=self.device_id)
                return False
                
            # 檢查"我的帳戶"連結是否存在
            my_account = frame.get_by_role("link", name="我的帳戶")
            if not my_account.is_visible():
                return False
                
            log_manager.info("登入成功，開始執行操作...", device=self.device_id)
            log_manager.debug("找到我的帳戶連結", device=self.device_id)
            
            # 檢查並點擊開啟按鈕
            open_button = frame.locator("#btnOpen div")
            if not open_button.is_visible():
                log_manager.debug("找不到開啟按鈕", device=self.device_id)
                return False
                
            open_button.click()
            log_manager.debug("點擊開啟按鈕", device=self.device_id)
            
            # 等待並檢查帳號連結出現 (最多 3 次嘗試，每次間隔 5 秒)
            check_attempts = 0
            max_check_attempts = 3
            account_visible = False
            account_link = frame.get_by_role("link", name=self.bank_account)

            while self.is_running and check_attempts < max_check_attempts:
                check_attempts += 1
                log_manager.debug(f"檢查帳號連結 {self.bank_account} 可見性... (嘗試 {check_attempts}/{max_check_attempts})", device=self.device_id)
                try:
                    # 嘗試等待元素變為可見
                    account_link.wait_for(state="visible", timeout=5000)
                    # 再次確認是否真的可見 (wait_for 可能成功但元素又消失)
                    if account_link.is_visible():
                        account_visible = True
                        break # 找到連結，跳出嘗試迴圈
                    else:
                         log_manager.debug(f"帳號連結 {self.bank_account} wait_for 成功但 is_visible 失敗 (嘗試 {check_attempts}/{max_check_attempts})", device=self.device_id)

                except Exception as wait_error:
                     log_manager.debug(f"等待帳號連結 {self.bank_account} 時發生錯誤 (嘗試 {check_attempts}/{max_check_attempts}): {wait_error}", device=self.device_id)
                     # 即使等待失敗，也繼續嘗試，因為 is_visible 可能在下一次檢查成功

                # 找不到連結或等待失敗，等待後重試 (除非是最後一次嘗試)
                if not account_visible and check_attempts < max_check_attempts:
                    log_manager.warning(f"找不到帳號連結 {self.bank_account}，將在 5 秒後重試 (嘗試 {check_attempts}/{max_check_attempts})", device=self.device_id)
                    time.sleep(5)
                elif not account_visible and check_attempts == max_check_attempts:
                    # 達到最大嘗試次數
                    log_manager.error(f"在 {max_check_attempts} 次嘗試後仍找不到帳號連結: {self.bank_account}", device=self.device_id)

            # 根據最終檢查結果執行操作
            if account_visible:
                account_link.click()
                log_manager.debug(f"成功點擊帳號連結: {self.bank_account}", device=self.device_id)
                
                # # 等待頁面載入
                # self.page.wait_for_load_state("networkidle")
                
                # # 等待並點擊交易明細查詢按鈕
                # detail_button = frame.get_by_role("button", name="交易明細查詢")
                # detail_button.wait_for(state="visible", timeout=5000)  # 等待 5 秒
                
                # if not detail_button.is_visible():
                #     log_manager.debug("找不到交易明細查詢按鈕", device=self.device_id)
                #     return False
                    
                # detail_button.click()
                # log_manager.debug("點擊交易明細查詢按鈕", device=self.device_id)
                
                # # 等待頁面載入完成
                # self.page.wait_for_load_state("networkidle")
                return True
                
            else:
                # 如果最終還是找不到帳號連結
                 self.log_with_screenshot('error',
                    f"找不到預期的帳號連結: {self.bank_account} ({max_check_attempts} 次嘗試失敗)",
                    "account_not_found")
                 return False

        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
            return False
            
    def should_refresh(self):
        """檢查是否需要在台北時間 00:00:00 重新整理"""
        taipei_tz = pytz.timezone('Asia/Taipei')
        now = datetime.now(taipei_tz)
        
        # 如果是新的一天且還沒重新整理過
        if (self.last_refresh_date is None or 
            now.date() > self.last_refresh_date):
            # 檢查是否在凌晨 00:00
            if now.hour == 0 and now.minute == 0:
                return True
        return False

    def perform_periodic_update(self):
        """執行定期更新（包含更新 cookies 和點擊重新計時）"""
        # 檢查是否需要重新整理
        if self.should_refresh():
            taipei_tz = pytz.timezone('Asia/Taipei')
            self.last_refresh_date = datetime.now(taipei_tz).date()
            log_manager.info("觸發每日重新整理", device=self.device_id)
            return True
        if not self.is_running:
            return
            
        try:
            current_time = time.time()
            # 檢查是否需要更新
            if current_time - self.last_update_time < self.update_interval:
                return
                
            # 點擊重新計時按鈕
            refresh_link = self.page.get_by_role("link", name="重新計時")
            if refresh_link.is_visible():
                refresh_link.click()
                log_manager.debug("點擊重新計時按鈕", device=self.device_id)
                
            # 獲取所有 cookies
            cookies = self.context.cookies()
            
            # 組合成完整的 cookie 字串
            cookie_parts = []
            required_cookies = ['v1st', 'manual_refresh', 'JSESSIONID', 'BDCUN', 'BDUID', 'LoginFlag', 'RESEGED']
            
            for cookie_name in required_cookies:
                cookie = next((c for c in cookies if c['name'] == cookie_name), None)
                if cookie:
                    cookie_parts.append(f"{cookie['name']}={cookie['value']}")
                    
            if len(cookie_parts) == len(required_cookies):
                cookie_string = "; ".join(cookie_parts)
                cookies_dict = {"Cookie": cookie_string}
                
                if self.update_cookies(cookies_dict):
                    self.cookies_updated = True
                    self.last_update_time = current_time
                    self.update_status("已更新 cookies 並重新計時")
                else:
                    self.log_with_screenshot('warning', 
                        "更新 cookies 失敗", 
                        "update_cookies_failed")
            else:
                missing_cookies = [name for name in required_cookies 
                                 if not any(c['name'] == name for c in cookies)]
                self.log_with_screenshot('warning', 
                    f"無法獲取完整的 cookies: {', '.join(missing_cookies)}", 
                    "incomplete_cookies")
                    
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', 
                    f"執行定期更新時發生錯誤: {str(e)}", 
                    "periodic_update_error")
                    
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待並獲取輸入欄位
            try:
                # 等待身分證字號欄位出現
                try:
                    id_textbox = self.page.get_by_role("textbox", name="請輸入身分證/統一編號")
                    id_textbox.wait_for(state="visible", timeout=5000)
                except TimeoutError:
                    log_manager.debug("自動填入失敗：等待身分證字號欄位超時", device=self.device_id)
                    return False
                
                # 使用 ID 定位輸入欄位
                id_input = self.page.locator("#loginCustId")
                user_id_input = self.page.locator("#usrIdInput")
                password_input = self.page.locator("#pwd")
                
                # 確保所有欄位都可見
                if not all([id_input.is_visible(), user_id_input.is_visible(), password_input.is_visible()]):
                    log_manager.debug("自動填入失敗：部分輸入欄位未顯示", device=self.device_id)
                    return False
            except Exception as e:
                log_manager.debug(f"自動填入失敗：等待輸入欄位超時 - {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 第一銀行專用選擇器
                    captcha_selectors = [
                        'img[src*="captcha"]',  # 通用驗證碼圖片
                        'img[src*="verify"]',
                        'img[src*="code"]',
                        'img[src*="data:image"]',  # base64 圖片
                        'img[alt*="驗證"]',
                        'img[title*="驗證"]'
                    ]
                    input_selectors = [
                        'input[placeholder*="驗證"]',  # 第一銀行專用
                        'input[name*="captcha"]',
                        'input[name*="verify"]',
                        'input[id*="captcha"]',
                        'input[id*="verify"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟第一銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                self.page.goto("https://ibank.firstbank.com.tw/NetBank/index103.html")
                self.page.wait_for_load_state("networkidle")
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                self.update_status("開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    try:
                        # 執行定期更新（包含更新 cookies 和點擊重新計時）
                        if self.perform_periodic_update():
                            # 需要重新整理，跳出內部循環
                            break
                        
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"監控過程中發生錯誤: {str(e)}",
                                "monitor_error")
                            time.sleep(5)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止第一銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
