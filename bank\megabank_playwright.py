"""
兆豐銀行操作模組 (Playwright 版本)
實作兆豐銀行特定的自動化操作
"""

import time
import json
import re
from playwright.sync_api import expect, TimeoutError
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class MegaBank(BankBase):
    """兆豐銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=False, use_proxy: bool = False): # 新增 is_mobile 和 use_proxy 參數
        # 兆豐銀行代號為 017，預設不使用手機版
        super().__init__(
            bank_code="017",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=is_mobile,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = "https://ebank.megabank.com.tw/nib/"  # 登入網址
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # Cookie 更新間隔（秒）
        self.stored_token = None  # 儲存已獲取的 token
        self.token_requested = False  # 追踪是否已請求過 token
        
    def wait_for_login(self):
        """等待登入成功"""
        while self.is_running:
            try:
                # 檢查"個人帳戶總覽"是否出現
                overview_heading = self.page.get_by_role("heading", name="個人帳戶總覽")
                if overview_heading.is_visible():
                    log_manager.debug("檢測到個人帳戶總覽", device=self.device_id)
                    return True
            except:
                pass
            time.sleep(2)
        return False

    def try_open_menu(self):
        """嘗試展開選單"""
        banner = self.page.get_by_role("banner")
        twd_menu = banner.get_by_text("台幣", exact=True)
        menu_item = self.page.locator("#sub-menu-A").get_by_text("存款交易明細")
        attempt = 1
        
        while self.is_running:
            try:
                # 滑鼠移到台幣選項上
                twd_menu.hover()
                log_manager.debug(f"第 {attempt} 次嘗試滑鼠移到台幣選項上", device=self.device_id)
                
                # 等待存款交易明細選項出現
                expect(menu_item).to_be_visible(timeout=5000)
                log_manager.debug("存款交易明細選項已出現", device=self.device_id)
                return True
                
            except Exception as e:
                log_manager.debug(f"第 {attempt} 次嘗試失敗: {str(e)}", device=self.device_id)
                attempt += 1
                time.sleep(1)  # 等待一下再試
                
        return False

    def check_account_exists(self):
        """檢查帳號是否存在於頁面中"""
        try:
            # 檢查頁面內容
            page_content = self.page.content()
            if self.bank_account in page_content:
                log_manager.debug(f"找到銀行帳號: {self.bank_account}", device=self.device_id)
                return True
        except:
            pass
        return False

    def navigate_to_account_page(self):
        """導航到帳戶頁面"""
        try:
            # 嘗試展開選單
            if not self.try_open_menu():
                return False
            
            # 點擊存款交易明細
            self.page.locator("#sub-menu-A").get_by_text("存款交易明細").click()
            log_manager.debug("點擊存款交易明細", device=self.device_id)
            
            # 檢查帳號是否出現 (最多 3 次嘗試，每次間隔 5 秒)
            log_manager.debug("等待帳號出現...", device=self.device_id)
            max_retries = 3
            retry_delay = 5
            account_found = False

            for attempt in range(max_retries):
                if not self.is_running: return False # 每次重試前檢查

                log_manager.debug(f"檢查帳號是否存在 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                if self.check_account_exists():
                    account_found = True
                    break # 找到帳號，跳出重試循環

                # 未找到帳號，處理重試
                if attempt < max_retries - 1:
                    log_manager.info(f"未找到帳號，等待 {retry_delay} 秒後重試...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                else:
                    # 最後一次嘗試失敗
                    self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍未在頁面找到帳號 {self.bank_account}", "account_check_retries_failed")
                    # 可以在此處決定是否停止，目前僅返回 False
                    # self.stop()
                    return False

            return account_found # 返回最終查找結果
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', f"導航到帳戶頁面時發生錯誤: {str(e)}", "navigation_error")
            return False
            
    def _handle_request(self, request):
        """處理請求事件，獲取 token"""
        if not self.is_running or not self.token_requested:
            return
            
        try:
            # 只在點擊查詢按鈕後監聽 API 請求以獲取 token
            if 'nibAdapter/api/json' in request.url and not self.stored_token:
                headers = request.headers
                token = headers.get('x-auth-token')
                
                if token:
                    log_manager.debug(f"獲取到 token: {token}", device=self.device_id)
                    self.stored_token = token
                    
                    # 更新到 API
                    if self.update_cookies({"token": token}):
                        self.cookies_updated = True
                        self.last_cookie_update = time.time()
                        self.update_status("已獲取並更新 token")
                        log_manager.info("成功獲取並更新 token 到 API", device=self.device_id)
                        
        except Exception as e:
            log_manager.error(f"處理請求時發生錯誤: {str(e)}", device=self.device_id)

    def check_and_handle_timeout(self):
        """檢查並處理操作逾時提醒"""
        try:
            # 檢查是否出現操作逾時提醒
            timeout_heading = self.page.get_by_role("heading", name="操作逾時提醒")
            if timeout_heading.is_visible():
                log_manager.debug("檢測到操作逾時提醒", device=self.device_id)
                
                # 點擊繼續使用按鈕
                continue_button = self.page.get_by_text("繼續使用", exact=True)
                continue_button.click()
                log_manager.debug("點擊繼續使用按鈕", device=self.device_id)
                return True
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"處理操作逾時提醒時發生錯誤: {str(e)}",
                    "timeout_handle_error")
        return False

    def perform_periodic_tasks(self):
        """執行定時任務：更新 token 到 API"""
        try:
            # 如果已有 stored_token，更新到 API
            if self.stored_token:
                if self.update_cookies({"token": self.stored_token}):
                    self.last_cookie_update = time.time()
                    self.update_status("已定時更新 token")
                    log_manager.info("成功定時更新 token 到 API", device=self.device_id)
                    return True
                else:
                    self.log_with_screenshot('error',
                        "更新 token 到 API 失敗",
                        "update_token_error")
                    return False
            else:
                self.log_with_screenshot('error',
                    "尚未獲取 token",
                    "token_not_found")
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"執行定時任務時發生錯誤: {str(e)}",
                    "periodic_task_error")
            return False

    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 使用 formcontrolname 定位輸入欄位
                id_input = self.page.locator("[formcontrolname='inputUserIxd']")
                user_id_input = self.page.locator("[formcontrolname='inputUserCode']")
                password_input = self.page.locator("[formcontrolname='inputPin']")
                
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 為兆豐銀行定義專用選擇器 - 基於實際 DOM 結構
                    captcha_selectors = [
                        'form section img[src*="data:image"]',  # 表單內 section 中的 base64 圖片
                        'app-fco02003-home img[src*="data:image"]',  # Angular 組件內的 base64 圖片
                        'section div img[src*="data:image"]',  # section 內的 base64 圖片
                        'img[src*="data:image"]',  # 任何 base64 圖片
                        'img[src*="captcha"]',  # 通用驗證碼圖片
                        'img[src*="verify"]',
                        'img[src*="code"]'
                    ]
                    input_selectors = [
                        'textbox[placeholder*="請輸入驗證碼"]',  # 兆豐銀行專用
                        'input[placeholder*="驗證碼"]',
                        'input[placeholder*="驗證"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
                
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 開啟網頁
                self.update_status("正在開啟兆豐銀行網頁...")
                try:
                    # 使用 domcontentloaded 事件，這比 networkidle 更快
                    self.page.goto(self.login_url, wait_until="domcontentloaded", timeout=5000)
                except Exception as e:
                    # 忽略超時錯誤，因為頁面可能已經載入完成
                    log_manager.debug("頁面載入可能未完全完成，但繼續執行", device=self.device_id)
                        
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                # 等待登入
                self.update_status("等待登入中...")
                if not self.wait_for_login():
                    continue
                    
                # 導航到帳戶頁面
                if not self.navigate_to_account_page():
                    continue
                    
                # 設置 token_requested 標記並點擊查詢按鈕以獲取初始 token
                self.token_requested = True
                query_button = self.page.locator("a").filter(has_text=re.compile(r"^查詢$"))
                expect(query_button).to_be_visible()
                query_button.click()
                log_manager.debug("點擊查詢按鈕以獲取初始 token", device=self.device_id)
                
                # 等待獲取 token
                time.sleep(2)
                self.token_requested = False
                
                if not self.stored_token:
                    self.log_with_screenshot('error',
                        "無法獲取初始 token",
                        "initial_token_error")
                    continue
                    
                self.update_status("登入成功，開始執行操作...")
                
                # 操作循環
                while self.is_running:
                    try:
                        current_time = time.time()
                        
                        # 檢查是否需要執行定時任務
                        if current_time - self.last_cookie_update >= self.cookie_update_interval:
                            self.perform_periodic_tasks()
                            
                        # 檢查是否出現操作逾時提醒
                        self.check_and_handle_timeout()
                            
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"執行操作時發生錯誤: {str(e)}",
                                "operation_error")
                            self.update_status("發生錯誤，等待重試...")
                            time.sleep(10)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    self.update_status("發生錯誤，等待重試...")
                    time.sleep(10)
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止兆豐銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
