import os
import logging
from logging.handlers import RotatingFileHandler
import re
from datetime import datetime, timedelta
import glob

try:
    from gui.utils._version import VERSION
except ImportError:
    VERSION = "開發版本"

class DeviceLoggerAdapter(logging.LoggerAdapter):
    def process(self, msg, kwargs):
        kwargs.setdefault('extra', {}).setdefault('device', self.extra.get('device', 'system'))
        return msg, kwargs

class LogManager:
    # 日誌級別定義
    LEVELS = {
        'DEBUG': logging.DEBUG,      # 10
        'INFO': logging.INFO,        # 20
        'WARNING': logging.WARNING,  # 30
        'ERROR': logging.ERROR,      # 40
        'CRITICAL': logging.CRITICAL # 50
    }
    
    def __init__(self, max_days=3, max_size_mb=5):
        """
        初始化日誌管理器
        :param max_days: 日誌保存最大天數
        :param max_size_mb: 單個日誌檔案最大大小（MB）
        """
        self.max_days = max_days
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.current_log_file = None
        
        # 創建日誌目錄
        if not os.path.exists('logs'):
            os.makedirs('logs')
            
        # 清理過期日誌
        self._cleanup_old_logs()
            
        # 創建基礎logger
        self.base_logger = logging.getLogger('bank_login')
        self.base_logger.setLevel(logging.DEBUG)
        
        # 設置日誌格式
        self.formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(device)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 創建新的日誌檔案
        self._create_new_log_file()
        
        # 控制台處理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(self.formatter)
        self.base_logger.addHandler(console_handler)
        
        # 創建適配器
        self.logger = DeviceLoggerAdapter(self.base_logger, {'device': 'system'})
        
        # 記錄啟動版本
        self.info(f"系統啟動 - 版本 {VERSION}")
            
    def _create_new_log_file(self):
        """創建新的日誌檔案"""
        # 移除現有的檔案處理器
        for handler in self.base_logger.handlers[:]:
            if isinstance(handler, RotatingFileHandler):
                self.base_logger.removeHandler(handler)
        
        # 生成新的檔案名稱，包含日期和時間
        current_time = datetime.now()
        self.current_log_file = f'logs/app_{current_time.strftime("%Y-%m-%d_%H%M%S")}.log'
        
        # 創建新的檔案處理器
        file_handler = RotatingFileHandler(
            self.current_log_file,
            maxBytes=self.max_size_bytes,
            backupCount=1,  # 當達到大小限制時，會自動創建新檔案，所以這裡設為1
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(self.formatter)
        
        # 設置檔案大小檢查回調
        def size_check(record):
            if os.path.exists(self.current_log_file) and \
               os.path.getsize(self.current_log_file) >= self.max_size_bytes:
                self._create_new_log_file()
            return True
        
        file_handler.addFilter(size_check)
        self.base_logger.addHandler(file_handler)
            
    def _cleanup_old_logs(self):
        """清理過期的日誌檔案"""
        try:
            current_time = datetime.now()
            log_files = glob.glob('logs/app_*.log*')
            
            for log_file in log_files:
                # 從檔案名稱中提取日期
                date_match = re.search(r'app_(\d{4}-\d{2}-\d{2})', log_file)
                if date_match:
                    file_date = datetime.strptime(date_match.group(1), '%Y-%m-%d')
                    # 如果檔案超過最大保存天數，則刪除
                    if (current_time - file_date).days > self.max_days:
                        try:
                            os.remove(log_file)
                            print(f"已刪除過期日誌: {log_file}")
                        except Exception as e:
                            print(f"刪除日誌檔案失敗 {log_file}: {str(e)}")
                            
        except Exception as e:
            print(f"清理日誌時發生錯誤: {str(e)}")
                
    def set_console_level(self, level):
        """設置控制台日誌級別"""
        if isinstance(level, str):
            level = self.LEVELS.get(level.upper(), logging.INFO)
        for handler in self.base_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and not isinstance(handler, RotatingFileHandler):
                handler.setLevel(level)
                
    def _log(self, level, message, device="system"):
        """內部日誌記錄方法"""
        self.logger.extra['device'] = device
        if level == logging.DEBUG:
            self.logger.debug(message)
        elif level == logging.INFO:
            self.logger.info(message)
        elif level == logging.WARNING:
            self.logger.warning(message)
        elif level == logging.ERROR:
            self.logger.error(message)
        elif level == logging.CRITICAL:
            self.logger.critical(message)
            
    def debug(self, message, device="system"):
        """記錄調試級別日誌"""
        self._log(logging.DEBUG, message, device)
        
    def info(self, message, device="system"):
        """記錄信息級別日誌"""
        self._log(logging.INFO, message, device)
        
    def warning(self, message, device="system"):
        """記錄警告級別日誌"""
        self._log(logging.WARNING, message, device)
        
    def error(self, message, device="system"):
        """記錄錯誤級別日誌"""
        self._log(logging.ERROR, message, device)
        
    def critical(self, message, device="system"):
        """記錄嚴重錯誤級別日誌"""
        self._log(logging.CRITICAL, message, device)
        
    def exception(self, message, device="system"):
        """記錄異常信息"""
        self.logger.extra['device'] = device
        self.logger.exception(message)
        
    def log(self, level, message, device="system"):
        """通用日誌記錄方法"""
        level = level.upper()
        if level == "DEBUG":
            self.debug(message, device)
        elif level == "INFO":
            self.info(message, device)
        elif level == "WARNING":
            self.warning(message, device)
        elif level == "ERROR":
            self.error(message, device)
        elif level == "CRITICAL":
            self.critical(message, device)
        
    def read_logs(self, level=None, device=None):
        """
        讀取當前日誌文件
        :param level: 日誌級別
        :param device: 設備名稱
        """
        try:
            logs = []
            
            # 讀取當前日誌檔案
            if self.current_log_file and os.path.exists(self.current_log_file):
                try:
                    with open(self.current_log_file, 'r', encoding='utf-8') as f:
                        logs.extend(f.readlines())
                except Exception as e:
                    print(f"讀取日誌檔案失敗 {self.current_log_file}: {str(e)}")
                
            filtered_logs = []
            for log in logs:
                # 如果指定了級別，檢查日誌級別是否匹配
                if level:
                    log_level = re.search(r' - (\w+) - ', log)
                    if not log_level:
                        continue
                    log_level = log_level.group(1)
                    
                    if self.LEVELS.get(log_level, 0) < self.LEVELS.get(level, 0):
                        continue
                        
                # 如果指定了設備，檢查設備是否匹配
                if device:
                    if f" - {device} - " not in log:
                        continue
                        
                filtered_logs.append(log.strip())
                
            return filtered_logs
            
        except Exception as e:
            print(f"讀取日誌時發生錯誤: {str(e)}")
            return []

# 創建全局日誌管理器實例
log_manager = LogManager()
