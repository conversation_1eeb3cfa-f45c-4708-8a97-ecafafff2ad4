import requests
import json
import os
from typing import List, Dict, Callable, Optional
from gui.utils.logger import log_manager
from gui.utils.config import config_manager
from bank import BankManager

class DeviceManager:
    """設備管理器類"""
    def __init__(self, app_state):
        self.app_state = app_state
        self.bank_manager = BankManager(device_manager=self)
        self.devices: List[Dict] = []
        self._last_fetch_time = 0
        self._fetch_lock = False
        
    def fetch_devices(self) -> List[Dict]:
        """獲取設備列表"""
        if self._fetch_lock:
            log_manager.warning("已有進行中的設備列表請求")
            return self.devices
            
        try:
            self._fetch_lock = True
            
            response = requests.get(
                config_manager.get_api_url("/api/twd/member/devices"),
                headers={"Authorization": self.app_state.current_token},
                timeout=30  # 添加超時設置
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    # 直接使用API返回的設備列表
                    self.devices = data.get("data", [])
                    return self.devices
                else:
                    raise Exception(data.get("message", "獲取設備列表失敗"))
            else:
                raise Exception(f"HTTP錯誤: {response.status_code}")
                
        except requests.exceptions.Timeout:
            log_manager.error("獲取設備列表超時")
            raise Exception("獲取設備列表超時，請稍後重試")
        except requests.exceptions.RequestException as e:
            log_manager.error(f"網路請求錯誤: {str(e)}")
            raise Exception("網路連接錯誤，請檢查網路狀態")
        except Exception as e:
            log_manager.error(f"獲取設備列表失敗: {str(e)}")
            raise
        finally:
            self._fetch_lock = False
            
    def get_device_by_id(self, device_id: str) -> Optional[Dict]:
        """根據ID獲取設備信息"""
        return next((d for d in self.devices if d["id"] == device_id), None)
        
    def start_bank_login(self, device_id: str, bank_code: str, bank_account: str):
        """啟動銀行登入"""
        try:
            # 檢查設備是否存在
            device = self.get_device_by_id(device_id)
            if not device:
                raise Exception("找不到指定的設備")
                
            # 檢查設備是否已在運行
            if device_id in self.bank_manager.banks:
                log_manager.warning(f"設備 {device_id} 已有運行中的登入程序", device=device_id)
                raise Exception("該設備已有運行中的登入程序")
                
            def status_callback(status):
                log_manager.info(status, device=device_id)
                
            # --- 從本地 device_settings.json 讀取代理設定 ---
            use_proxy = False # 預設不使用代理
            settings_path = 'device_settings.json'
            try:
                if os.path.exists(settings_path):
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                    device_settings = settings.get("devices", {}).get(str(device_id), {})
                    use_proxy = device_settings.get("use_proxy", False)
            except Exception as e:
                log_manager.error(f"讀取設備 {device_id} 的本地代理設定時發生錯誤: {e}", device=device_id)
            # --- 讀取結束 ---
            log_manager.debug(f"設備 {device_id} 的代理設定 (從本地讀取): {use_proxy}", device=device_id)

            # 啟動銀行登入，傳遞 use_proxy 設定
            self.bank_manager.start_bank_login(
                bank_code=bank_code,
                bank_account=bank_account,
                device_id=device_id,
                status_callback=status_callback,
                use_proxy=use_proxy, # 傳遞代理設定
                device_data=device # 傳遞完整的設備資料給 BankManager
            )
            
        except Exception as e:
            log_manager.exception(f"啟動銀行登入時發生錯誤: {str(e)}", device=device_id)
            raise
            
    def stop_bank_login(self, device_id: str):
        """停止銀行登入"""
        try:
            # 停止銀行登入
            self.bank_manager.stop_bank_login(device_id)
            
        except Exception as e:
            log_manager.exception(f"停止銀行登入時發生錯誤: {str(e)}", device=device_id)
            raise
            
    def is_device_running(self, device_id: str) -> bool:
        """檢查設備是否正在運行"""
        try:
            # 只檢查設備是否在運行中的銀行實例列表中
            return device_id in self.bank_manager.banks
            
        except Exception as e:
            log_manager.error(f"檢查設備運行狀態時發生錯誤: {str(e)}", device=device_id)
            return False
        
    def add_device(self, device_data: Dict) -> Dict:
        """新增設備"""
        try:
            response = requests.post(
                config_manager.get_api_url("/api/twd/member/devices"),
                headers={
                    "Authorization": self.app_state.current_token,
                    "Accept": "*/*",
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                data=device_data,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return data.get("data", {})
                else:
                    raise Exception(data.get("message", "新增設備失敗"))
            else:
                raise Exception(f"HTTP錯誤: {response.status_code}")
                
        except requests.exceptions.Timeout:
            log_manager.error("新增設備超時")
            raise Exception("新增設備超時，請稍後重試")
        except requests.exceptions.RequestException as e:
            log_manager.error(f"網路請求錯誤: {str(e)}")
            raise Exception("網路連接錯誤，請檢查網路狀態")
        except Exception as e:
            log_manager.error(f"新增設備失敗: {str(e)}")
            raise
            
    def update_device(self, device_id: str, device_data: Dict) -> Dict:
        """更新設備"""
        try:
            response = requests.put(
                config_manager.get_api_url(f"/api/twd/member/devices/{device_id}"),
                headers={
                    "Authorization": self.app_state.current_token,
                    "Accept": "*/*",
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                data=device_data,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return data.get("data", {})
                else:
                    raise Exception(data.get("message", "更新設備失敗"))
            else:
                raise Exception(f"HTTP錯誤: {response.status_code}")
                
        except requests.exceptions.Timeout:
            log_manager.error("更新設備超時")
            raise Exception("更新設備超時，請稍後重試")
        except requests.exceptions.RequestException as e:
            log_manager.error(f"網路請求錯誤: {str(e)}")
            raise Exception("網路連接錯誤，請檢查網路狀態")
        except Exception as e:
            log_manager.error(f"更新設備失敗: {str(e)}")
            raise
            
    def get_statistics(self) -> Dict:
        """獲取設備統計信息"""
        try:
            total = len(self.devices)
            online = sum(1 for d in self.devices if d["status"] == 1)
            offline = total - online
            
            return {
                "total": total,
                "online": online,
                "offline": offline
            }
        except Exception as e:
            log_manager.error(f"獲取設備統計信息時發生錯誤: {str(e)}")
            return {"total": 0, "online": 0, "offline": 0}
