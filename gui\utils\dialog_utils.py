import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *

class DialogBase(tk.Toplevel):
    """對話框基礎類"""
    def __init__(self, parent, title, min_width=None, min_height=None, max_width=None, max_height=None):
        super().__init__(parent)
        self.parent = parent
        
        self.title(title)
        
        # 根據螢幕大小調整視窗大小
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        
        # 計算合適的視窗大小（根據螢幕大小的比例）
        dialog_width = min(int(screen_width * 0.3), max_width if max_width else screen_width)
        dialog_height = min(int(screen_height * 0.5), max_height if max_height else screen_height)
        
        # 設置視窗大小和位置（置中）
        x = (screen_width - dialog_width) // 2
        y = (screen_height - dialog_height) // 2
        self.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
        
        # 設置最小/最大尺寸
        if min_width and min_height:
            self.minsize(min_width, min_height)
        if max_width and max_height:
            self.maxsize(max_width, max_height)
            
        self.resizable(True, True)
        self.transient(parent)
        self.grab_set()
        
        # 初始化UI
        self._init_ui()
        
        # 綁定回車鍵和ESC鍵
        self.bind('<Return>', lambda e: self.on_confirm())
        self.bind('<Escape>', lambda e: self.destroy())
        
    def _init_ui(self):
        """初始化UI，子類需要實現此方法"""
        raise NotImplementedError
        
    def on_confirm(self):
        """確認按鈕事件，子類需要實現此方法"""
        raise NotImplementedError
