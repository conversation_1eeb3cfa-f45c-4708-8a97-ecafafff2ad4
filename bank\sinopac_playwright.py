"""
永豐銀行操作模組 (Playwright 版本)
實作永豐銀行特定的自動化操作
"""

import time
import json
import re
from playwright.sync_api import expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class SinopacBank(BankBase):
    """永豐銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=False, use_proxy: bool = False): # 新增 is_mobile 和 use_proxy 參數
        super().__init__(
            bank_code="807",  # 永豐銀行代碼
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=is_mobile,  # 支援手機版設定
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.expected_account = bank_account  # 保存初始帳號用於驗證
        self.login_url = "https://mma.sinopac.com/MemberPortal/Member/MMALogin.aspx"
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # Cookie 更新間隔（秒）
        self.refresh_interval = 300  # 頁面刷新間隔（秒）
        self.last_refresh = 0  # 上次刷新時間
        self.stored_cookies = None  # 儲存已獲取的 cookies
        
    def verify_account(self):
        """驗證帳號 (包含重試機制)"""
        if not self.is_running:
            return False

        max_retries = 3
        retry_delay = 5
        account_verified = False

        for attempt in range(max_retries):
            if not self.is_running: return False # 每次重試前檢查

            log_manager.debug(f"開始驗證永豐個人帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
            try:
                # 等待帳號表格出現
                table = self.page.locator("table.data-table-main2")
                try:
                    table.wait_for(state="visible", timeout=10000) # 增加等待時間
                except Exception as table_error: # 更廣泛地捕捉等待錯誤
                     log_manager.warning(f"第 {attempt + 1} 次嘗試找不到帳號表格或超時: {table_error}", device=self.device_id)
                     # 表格找不到，進入重試邏輯
                     if attempt < max_retries - 1:
                         log_manager.info(f"等待 {retry_delay} 秒後重試...", device=self.device_id)
                         for _ in range(retry_delay):
                             if not self.is_running: return False
                             time.sleep(1)
                         continue # 繼續下一次重試
                     else:
                         self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍找不到帳戶表格", "table_not_found_retries_failed")
                         self.stop()
                         return False

                # 遍歷表格中的所有資料列來尋找匹配的帳號
                rows = table.locator("tr.row") # 獲取所有資料列
                expected_numbers = re.sub(r'[^0-9]', '', self.expected_account)
                found_account_number = None # 用於記錄實際找到的帳號，方便日誌記錄
                account_verified = False # 重置驗證狀態

                log_manager.debug(f"表格共有 {rows.count()} 行，開始遍歷查找帳號 {expected_numbers}", device=self.device_id)

                for i in range(rows.count()):
                    row = rows.nth(i)
                    # 檢查是否為表頭行 (根據 th 標籤判斷)
                    if row.locator("th").count() > 0:
                        log_manager.debug(f"跳過第 {i+1} 行 (表頭)", device=self.device_id)
                        continue

                    try:
                        # 定位到第一個 td
                        account_cell = row.locator("td").first
                        account_cell.wait_for(state="visible", timeout=3000) # 縮短單元格等待時間

                        # 獲取 td 內的所有 p 標籤
                        p_tags = account_cell.locator("p")
                        if p_tags.count() >= 2:
                            # 獲取第二個 p 標籤的文字 (帳號通常在這裡)
                            account_p = p_tags.nth(1)
                            account_text = account_p.inner_text()
                            # 從帳號文字中提取數字 (保留連字號再移除，避免誤判)
                            current_account_numbers = re.sub(r'[^0-9-]', '', account_text).replace('-', '')
                            found_account_number = current_account_numbers # 記錄最後檢查的帳號

                            log_manager.debug(f"檢查第 {i+1} 行，找到帳號: {current_account_numbers}", device=self.device_id)

                            if current_account_numbers == expected_numbers:
                                log_manager.info(f"在第 {i+1} 行找到匹配帳號: {current_account_numbers} (嘗試 {attempt + 1})", device=self.device_id)
                                account_verified = True
                                break # 找到匹配，跳出內部循環
                        else:
                            log_manager.debug(f"第 {i+1} 行第一個單元格的 p 標籤數量不足 2，跳過", device=self.device_id)

                    except Exception as row_error:
                        log_manager.warning(f"處理第 {i+1} 行時發生錯誤: {row_error}", device=self.device_id)
                        # 可以選擇 continue 或記錄更詳細的錯誤

                # 迴圈結束後檢查是否驗證成功
                if account_verified:
                    break # 驗證成功，跳出外部重試循環
                else:
                    # 如果循環結束仍未找到匹配帳號
                    log_message = f"第 {attempt + 1} 次嘗試未在表格中找到預期帳號: {expected_numbers}"
                    if found_account_number: # 如果至少找到了一個帳號但都不匹配
                        log_message += f" (最後檢查的帳號: {found_account_number})"
                    log_manager.warning(log_message, device=self.device_id)
                    # 帳號不符，進入重試邏輯 (這部分保持不變)

                # --- 重試處理 ---
                if not account_verified and attempt < max_retries - 1:
                    log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                elif not account_verified and attempt == max_retries - 1:
                    # 最後一次嘗試失敗
                    self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後帳號仍不符 (預期: {expected_numbers}, 實際: {account_numbers})", "account_mismatch_retries_failed")
                    self.stop() # 驗證失敗，停止
                    return False

            except Exception as e:
                log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(e)}", device=self.device_id)
                if attempt < max_retries - 1:
                    log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                else:
                    self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(e)}", "account_verify_error_final")
                    self.stop()
                    return False

        # --- 循環結束後 ---
        if account_verified:
            return True
        else:
            # 理論上如果失敗會在循環內 return False
            log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
            if self.is_running: self.stop() # 確保停止
            return False
            
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 等待 "我的帳戶" 標題出現
            heading = self.page.get_by_role("heading", name="我的帳戶")
            if heading.is_visible():
                log_manager.debug("登入狀態確認成功", device=self.device_id)
                return True
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
                self.stop()
            return False
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies"""
        if not self.is_running:
            return False
            
        try:
            # 獲取當前頁面的所有 cookies
            all_cookies = self.context.cookies()
            
            # 將 cookies 組合成字串
            cookies_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in all_cookies])
            
            # 建立 cookies 物件
            cookies_data = {
                "Cookie": cookies_string
            }
            
            # 更新到 API
            if self.update_cookies(cookies_data):
                self.last_cookie_update = time.time()
                self.update_status("已更新 cookies")
                log_manager.debug("成功更新 cookies 到 API", device=self.device_id)
                return True
            else:
                log_manager.error("更新 cookies 到 API 失敗", device=self.device_id)
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"更新 cookies 時發生錯誤: {str(e)}",
                    "update_cookies_error")
            return False
            
    def refresh_page(self):
        """重新整理頁面"""
        if not self.is_running:
            return False
            
        try:
            # 重新整理頁面
            self.page.reload()
            
            # 等待頁面載入完成
            self.page.wait_for_load_state("networkidle")
            
            self.last_refresh = time.time()
            log_manager.debug("成功重新整理頁面", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"刷新頁面時發生錯誤: {str(e)}",
                    "refresh_error")
            return False
            
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待身分證字號標籤出現
            try:
                id_label = self.page.get_by_text("身分證字號(統一編號)")
                id_label.wait_for(state="visible", timeout=10000)
            except Exception as e:
                log_manager.debug(f"等待身分證字號標籤超時: {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 填入身分證字號
                id_input = self.page.get_by_role("textbox").nth(0)
                id_input.fill(self.device_data["bank_id_number"])
                
                # 填入使用者代號
                user_id_input = self.page.get_by_role("textbox").nth(1)
                user_id_input.fill(self.device_data["bank_user_id"])
                
                # 填入密碼
                password_input = self.page.get_by_role("textbox").nth(2)
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 為永豐銀行定義專用選擇器
                    captcha_selectors = [
                        'img[alt="圖形驗證碼"]',  # 永豐銀行專用
                        'link[title="圖形驗證碼"] img',
                        'img[src*="captcha"]',
                        'img[src*="verify"]'
                    ]
                    input_selectors = [
                        'textbox[placeholder="可按圖更換驗證碼"]',  # 永豐銀行專用
                        'input[placeholder*="驗證"]',
                        'input[name*="captcha"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                return True
                
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟永豐銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 訪問登入頁面
                self.page.goto(self.login_url)
                self.page.wait_for_load_state("networkidle")
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                        
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                self.update_status("登入成功，驗證帳號...")
                
                # 驗證帳號
                if not self.verify_account():
                    break  # verify_account 會自動呼叫 stop()
                    
                self.update_status("帳號驗證成功，開始執行操作...")
                
                # 首次更新 cookies
                if not self.get_and_update_cookies():
                    log_manager.error("首次更新 cookies 失敗", device=self.device_id)
                    break
                    
                # 操作循環
                while self.is_running:
                    try:
                        current_time = time.time()
                        
                        # 檢查是否需要更新 cookies
                        if current_time - self.last_cookie_update >= self.cookie_update_interval:
                            self.get_and_update_cookies()
                            
                        # 檢查是否需要刷新頁面
                        if current_time - self.last_refresh >= self.refresh_interval:
                            self.refresh_page()
                            
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"執行操作時發生錯誤: {str(e)}",
                                "operation_error")
                            self.update_status("發生錯誤，等待重試...")
                            time.sleep(10)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    self.update_status("發生錯誤，等待重試...")
                    time.sleep(10)
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
                
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止永豐銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
