"""
Line Bank 操作模組 (Playwright 版本)
實作 Line Bank 特定的自動化操作
"""

import time
import json
import re
from datetime import datetime
from playwright.sync_api import expect, TimeoutError
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class LineBank(BankBase):
    """Line Bank 操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        # 設定 is_mobile=False 使用電腦版介面
        super().__init__(
            bank_code="824", # Line Bank 代碼為 824
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=False,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = "https://accessibility.linebank.com.tw/transaction"
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        self.stored_cookies = None  # 儲存已獲取的 cookies
        self.arr_id = None  # 儲存 arrId
        
    def check_login_status(self, timeout=5000):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 檢查是否看到帳戶交易明細查詢標題
            heading = self.page.get_by_role("heading", name="帳戶交易明細查詢")
            try:
                heading.wait_for(state="visible", timeout=timeout)
                log_manager.debug("登入狀態確認成功", device=self.device_id)
                return True
            except TimeoutError:
                log_manager.debug("等待登入狀態超時，繼續等待", device=self.device_id)
                return False
                    
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
            return False

    def get_arr_id(self):
        """從網路請求中獲取 arrId"""
        if not self.is_running:
            return None

        try:
            # 重置 arrId
            self.arr_id = None
            
            # 監聽網路請求以獲取 arrId
            def handle_request(request):
                if request.url.endswith('/transactions') and request.method == 'POST':
                    try:
                        post_data = json.loads(request.post_data)
                        if 'arrId' in post_data:
                            self.arr_id = post_data['arrId']
                            log_manager.debug(f"成功獲取 arrId: {self.arr_id}", device=self.device_id)
                    except:
                        pass

            # 設置請求監聽
            self.page.on('request', handle_request)

            # 重整頁面以觸發請求
            log_manager.debug("重整頁面以獲取 arrId", device=self.device_id)
            self.page.reload()
            
            try:
                # 等待頁面載入完成
                self.page.wait_for_load_state("networkidle", timeout=10000)
            except TimeoutError:
                log_manager.debug("頁面重整載入超時", device=self.device_id)
                pass

            # 等待一段時間以確保能夠捕獲到請求
            time.sleep(3)

            if not self.arr_id:
                log_manager.debug("重整後仍未獲取到 arrId，嘗試點擊查詢", device=self.device_id)
                # 如果還是沒有獲取到 arrId，嘗試點擊查詢按鈕
                try:
                    query_button = self.page.get_by_role("button", name="查詢")
                    query_button.click()
                    time.sleep(3)
                except:
                    pass

            return self.arr_id

        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"獲取 arrId 時發生錯誤: {str(e)}",
                    "get_arr_id_error")
            return None
            
    def verify_account(self, timeout=5000):
        """驗證帳號"""
        if not self.is_running:
            return False
            
        try:
            # --- 帳號驗證重試邏輯 ---
            max_retries = 3
            retry_delay = 5
            account_verified = False
            account_heading_locator = self.page.get_by_role("heading", name=f"主帳戶 ({self.bank_account})")

            for attempt in range(max_retries):
                if not self.is_running: return False # 每次重試前檢查

                log_manager.debug(f"開始驗證帳號標題 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                try:
                    # 嘗試等待標題可見
                    account_heading_locator.wait_for(state="visible", timeout=timeout) # 使用傳入的 timeout
                    log_manager.info(f"帳號驗證成功: {self.bank_account} (嘗試 {attempt + 1})", device=self.device_id)
                    account_verified = True
                    break # 驗證成功，跳出重試循環

                except TimeoutError:
                    log_manager.warning(f"第 {attempt + 1} 次嘗試未找到帳號標題或超時: {self.bank_account}", device=self.device_id)
                    # 未找到或超時，處理重試
                    if attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    else:
                        # 最後一次嘗試失敗
                        self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍未找到帳號標題 {self.bank_account}", "account_verify_retries_failed")
                        self.stop() # 驗證失敗，停止
                        return False
                except Exception as inner_e:
                     # 捕獲 wait_for 期間的其他潛在錯誤
                     log_manager.error(f"驗證帳號標題時發生未預期錯誤 (嘗試 {attempt + 1}): {str(inner_e)}", device=self.device_id)
                     if attempt < max_retries - 1:
                         log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                         for _ in range(retry_delay):
                             if not self.is_running: return False
                             time.sleep(1)
                     else:
                         self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(inner_e)}", "account_verify_error_final")
                         self.stop() # 最後一次嘗試也出錯，則停止
                         return False

            # --- 循環結束後 ---
            if account_verified:
                return True
            else:
                # 理論上如果失敗會在循環內 return False
                log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
                if self.is_running: self.stop() # 確保停止
                return False

        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"驗證帳號時發生錯誤: {str(e)}",
                    "account_verify_error")
                self.stop()  # 發生錯誤時停止執行緒
            return False
            
    def check_and_handle_logout_notice(self):
        """檢查並處理登出提醒視窗"""
        if not self.is_running:
            return
            
        try:
            # 檢查是否出現登出系統提醒
            logout_heading = self.page.get_by_role("heading", name="登出系統提醒")
            if logout_heading.is_visible():
                # 點擊繼續使用按鈕
                continue_button = self.page.get_by_label("繼續使用LINE Bank 友善網路銀行")
                continue_button.click()
                log_manager.debug("已處理登出系統提醒", device=self.device_id)
                
        except Exception as e:
            # 忽略錯誤，因為這是持續監控的操作
            pass
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies"""
        if not self.is_running:
            return False
            
        try:
            current_time = time.time()
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update < self.cookie_update_interval:
                return True
                
            # 獲取所有 cookies
            cookies = self.context.cookies()
            
            # 組合所有 cookie 成字串
            cookie_str = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
            
            # 確保有 arrId
            if not self.arr_id:
                self.get_arr_id()
                
            if not self.arr_id:
                self.log_with_screenshot('error',
                    "無法獲取 arrId",
                    "missing_arr_id")
                return False
            
            # 組合成正確的格式，包含 Cookie 和 arrId
            cookies_dict = {
                "Cookie": cookie_str,
                "arrId": self.arr_id
            }
            
            if self.update_cookies(cookies_dict):
                self.cookies_updated = True
                self.last_cookie_update = current_time
                self.update_status("已更新 cookies 和 arrId")
                log_manager.debug(f"成功更新 cookies 和 arrId: {self.arr_id}", device=self.device_id)
                return True
            else:
                self.log_with_screenshot('warning',
                    "更新 cookies 失敗",
                    "update_cookies_failed")
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"獲取 cookies 時發生錯誤: {str(e)}",
                    "get_cookies_error")
            return False
            
    def refresh_page(self):
        """重新整理頁面並更新 cookies"""
        if not self.is_running:
            return False
            
        try:
            log_manager.debug("開始重整頁面", device=self.device_id)
            self.update_status("重整頁面以保持登入狀態...")
            
            # 重整頁面
            self.page.reload()
            
            try:
                # 等待頁面載入完成
                self.page.wait_for_load_state("networkidle", timeout=10000)
            except TimeoutError:
                self.log_with_screenshot('error',
                    "頁面重整後載入超時",
                    "reload_timeout")
                return False
                
            # 驗證帳號
            if not self.verify_account():
                return False
                
            # 更新 cookies
            if not self.get_and_update_cookies():
                return False
                
            log_manager.debug("頁面重整完成", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"重整頁面時發生錯誤: {str(e)}",
                    "refresh_error")
            return False
            
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待並獲取輸入欄位
            try:
                # 等待身分證字號欄位出現
                try:
                    id_input = self.page.get_by_placeholder("請輸入身分證字號")
                    id_input.wait_for(state="visible", timeout=5000)
                except TimeoutError:
                    log_manager.debug("自動填入失敗：等待身分證字號欄位超時", device=self.device_id)
                    return False
                
                # 使用 ID 定位輸入欄位
                user_id_input = self.page.locator("#userId")
                password_input = self.page.locator("#pw")
                
                # 確保所有欄位都可見
                if not all([id_input.is_visible(), user_id_input.is_visible(), password_input.is_visible()]):
                    log_manager.debug("自動填入失敗：部分輸入欄位未顯示", device=self.device_id)
                    return False
            except Exception as e:
                log_manager.debug(f"自動填入失敗：等待輸入欄位超時 - {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 連線銀行專用選擇器
                    captcha_selectors = [
                        'img[src*="captcha"]',  # 通用驗證碼圖片
                        'img[src*="verify"]',
                        'img[src*="code"]',
                        'img[src*="data:image"]',  # base64 圖片
                        'img[alt*="驗證"]',
                        'img[title*="驗證"]'
                    ]
                    input_selectors = [
                        'input[placeholder*="驗證"]',  # 連線銀行專用
                        'input[name*="captcha"]',
                        'input[name*="verify"]',
                        'input[id*="captcha"]',
                        'input[id*="verify"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟 Line Bank 網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 導向登入頁面
                self.page.goto(self.login_url)
                try:
                    self.page.wait_for_load_state("networkidle", timeout=10000)
                except TimeoutError:
                    self.log_with_screenshot('error',
                        "頁面載入超時",
                        "page_load_timeout")
                    continue
                    
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:  # 檢查是否已停止
                    break
                    
                self.update_status("登入成功，驗證帳號...")
                
                # 驗證帳號
                if not self.verify_account():
                    break  # verify_account 會自動呼叫 stop()
                
                # 在登入成功後主動獲取 arrId
                self.update_status("正在獲取必要資訊...")
                if not self.get_arr_id():
                    self.log_with_screenshot('error',
                        "無法獲取必要資訊",
                        "initial_arr_id_failed")
                    break
                    
                self.update_status("帳號驗證成功，開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    try:
                        # 檢查並處理登出提醒視窗
                        self.check_and_handle_logout_notice()
                        
                        # 檢查是否需要更新 cookies
                        self.get_and_update_cookies()
                        
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"監控過程中發生錯誤: {str(e)}",
                                "monitor_error")
                            time.sleep(5)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止 Line Bank 實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
