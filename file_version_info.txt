
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 3, 21, 0),
    prodvers=(1, 3, 21, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Bank Login Manager'),
         StringStruct(u'FileDescription', u'銀行登入管理系統 - 安全的銀行帳戶管理工具'),
         StringStruct(u'FileVersion', u'1.3.21'),
         StringStruct(u'InternalName', u'bank_login'),
         StringStruct(u'LegalCopyright', u'Copyright (c) 2024 Bank Login Manager'),
         StringStruct(u'OriginalFilename', u'銀行登入管理系統_1.3.21.exe'),
         StringStruct(u'ProductName', u'銀行登入管理系統'),
         StringStruct(u'ProductVersion', u'1.3.21'),
         StringStruct(u'Comments', u'安全的銀行帳戶管理工具')])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
