import sys
import os
import json

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入PIL補丁，解決ANTIALIAS問題
import pil_patch

# 導入GUI應用
from gui.app import BankLoginApp
from gui.utils.logger import log_manager
from proxy_scripts.start_proxy import kill_all_mitmdump, cleanup_all_locks # <--- 新增導入清理函數
from bank.ocr_manager import ocr_manager  # 導入 OCR 管理器

def init_device_settings():
    """初始化 device_settings.json"""
    settings_path = 'device_settings.json'
    if not os.path.exists(settings_path):
        default_settings = {
            "devices": {},
            "global": {}
        }
        try:
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(default_settings, f, indent=2, ensure_ascii=False)
            log_manager.debug("已建立 device_settings.json")
        except Exception as e:
            log_manager.error(f"建立 device_settings.json 時發生錯誤: {str(e)}")
            sys.exit(1)

def main():
    try:
        # 檢查必要的字體文件
        font_path = os.path.join(current_dir, 'fonts', 'Noto_Sans_TC', 'static', 'NotoSansTC-Regular.ttf')
        if not os.path.exists(font_path):
            print(f"錯誤: 找不到必要的字體文件: {font_path}")
            sys.exit(1)
            
        # 初始化設定檔
        init_device_settings()

        # --- 新增：應用啟動時清理 mitmproxy ---
        log_manager.info("應用程式啟動：開始清理舊的 mitmproxy 進程和鎖定文件...")
        kill_all_mitmdump()
        cleanup_all_locks()
        log_manager.info("應用程式啟動：清理完成。")
        # --- 清理結束 ---

        # 初始化 OCR 功能
        log_manager.info("正在檢查 OCR 功能...")
        ocr_manager.check_installation()

        # 啟動應用程序
        app = BankLoginApp()
        app.mainloop()
        
    except Exception as e:
        print(f"應用程序啟動失敗: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
