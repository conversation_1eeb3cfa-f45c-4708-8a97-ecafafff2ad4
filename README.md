# 銀行登入系統

這是一個用於管理銀行設備和自動登入銀行網頁的 Windows 應用程式。

## 功能特點

### 會員系統
- 會員登入
- Token 管理
- 登入狀態維護

### 設備管理
- 查看設備列表
- 新增設備
- 監控設備狀態
- 銀行登入控制

### 銀行登入
- 支援玉山銀行和台新銀行
- 半自動登入流程
- 自動更新 Cookies
- 即時狀態回饋

### 報表功能
- 查看交易記錄
- 多種日期範圍篩選
  - 今日
  - 昨日
  - 本周
  - 上周
  - 本月
  - 上月
- 詳細的統計數據
- 分頁顯示

### 系統日誌
- 操作日誌記錄
- 即時狀態顯示

## 系統需求

- Windows 11
- Python 3.8 或以上
- Chrome 瀏覽器

## 安裝步驟

1. 安裝 Python 依賴套件：
```bash
pip install -r requirements.txt
```

2. 執行程式：
```bash
python main.py
```

## 使用說明

### 1. 登入系統
- 輸入會員帳號和密碼
- 登入成功後會進入主畫面

### 2. 設備管理
- 點擊「新增設備」可以註冊新的銀行設備
- 在設備列表中選擇設備後可以：
  - 查看設備詳細資訊
  - 開始銀行登入
  - 停止銀行登入

### 3. 銀行登入流程
1. 在設備列表中選擇要登入的設備
2. 點擊「登入銀行」按鈕
3. 系統會開啟瀏覽器視窗
4. 手動完成銀行登入
5. 系統會自動維持登入狀態並更新 Cookies

### 4. 報表查詢
- 選擇要查詢的設備（可選擇全部）
- 選擇日期範圍
- 點擊查詢按鈕
- 查看交易記錄和統計資訊

## 注意事項

1. 銀行登入為半自動化流程，需要使用者手動完成實際的登入步驟
2. 系統會自動維護 Cookies 狀態，無需手動更新
3. 所有敏感資訊都經過安全處理
4. 建議定期查看系統日誌以掌握運行狀態

## 錯誤排除

### 常見問題

1. 無法登入系統
- 檢查網路連線
- 確認帳號密碼正確
- 確認 API 服務是否正常

2. 銀行登入失敗
- 確認瀏覽器是否正常運作
- 檢查網路連線
- 確認銀行網站是否可以訪問

3. 報表查詢失敗
- 檢查登入狀態是否有效
- 確認查詢條件是否正確
- 檢查網路連線

### 如何回報問題

如果遇到無法解決的問題，請提供以下資訊：
1. 錯誤發生的時間
2. 執行的操作步驟
3. 錯誤訊息內容
4. 系統日誌內容

## 更新記錄

### v1.0.0 (2024-01)
- 初始版本發布
- 支援玉山銀行和台新銀行
- 基本的設備管理功能
- 報表查詢功能
- 系統日誌功能

# 客戶端需安裝
node.js
然後執行
`npm init playwright@latest`

playwright 測試用指令
playwright codegen --device="iPhone 12" https://example.com


.\venv\Scripts\Activate.ps1; python .\build.py