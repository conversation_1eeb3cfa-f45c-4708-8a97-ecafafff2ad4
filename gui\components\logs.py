import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from datetime import datetime
from gui.utils.logger import log_manager
import logging

class LogsFrame(ttk.LabelFrame):
    def __init__(self, parent):
        super().__init__(parent, text="系統日誌", padding=10)
        self.parent = parent
        self.last_log_count = 0  # 用於追踪日誌數量變化
        self.auto_scroll = True  # 是否自動滾動到底部
        self.devices = []  # 存儲設備列表
        log_manager.debug("初始化日誌顯示框架")
        self._init_ui()
        
    def _init_ui(self):
        try:
            # 設置樣式
            style = ttk.Style()
            style.configure("Logs.TLabel",
                font=("Noto Sans TC", 10)
            )
            style.configure("Logs.TCombobox",
                font=("Noto Sans TC", 10)
            )
            style.configure("Logs.TCheckbutton",
                font=("Noto Sans TC", 10)
            )
            
            # 上方控制框架
            top_control_frame = ttk.Frame(self)
            top_control_frame.pack(fill=X, pady=(0, 5))
            
            # 設備選擇
            ttk.Label(top_control_frame, text="設備:", style="Logs.TLabel").pack(side=LEFT)
            self.device_var = tk.StringVar(value="全部")
            self.device_combo = ttk.Combobox(
                top_control_frame,
                textvariable=self.device_var,
                values=["全部", "system"],
                state="readonly",
                width=20,
                style="Logs.TCombobox"
            )
            self.device_combo.pack(side=LEFT, padx=(5, 10))
            
            # 日誌級別選擇
            ttk.Label(top_control_frame, text="級別:", style="Logs.TLabel").pack(side=LEFT)
            self.level_var = tk.StringVar(value="INFO")
            level_combo = ttk.Combobox(
                top_control_frame,
                textvariable=self.level_var,
                values=["ALL", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                state="readonly",
                width=10,
                style="Logs.TCombobox"
            )
            level_combo.pack(side=LEFT, padx=(5, 10))
            
            # 下方控制框架
            bottom_control_frame = ttk.Frame(self)
            bottom_control_frame.pack(fill=X, pady=(0, 5))
            
            # 自動刷新選項
            self.auto_refresh_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(
                bottom_control_frame,
                text="自動刷新",
                variable=self.auto_refresh_var,
                style="Logs.TCheckbutton"
            ).pack(side=LEFT, padx=10)
            
            # 自動滾動選項
            self.auto_scroll_var = tk.BooleanVar(value=True)
            ttk.Checkbutton(
                bottom_control_frame,
                text="自動滾動",
                variable=self.auto_scroll_var,
                style="Logs.TCheckbutton"
            ).pack(side=LEFT, padx=10)
            
            # 刷新按鈕
            ttk.Button(
                bottom_control_frame,
                text="刷新",
                command=self.refresh_logs,
                style="primary.TButton"
            ).pack(side=LEFT, padx=5)
            
            # 清除按鈕
            ttk.Button(
                bottom_control_frame,
                text="清除顯示",
                command=self.clear_display,
                style="secondary.TButton"
            ).pack(side=RIGHT)
            
            # 日誌文本框架
            text_frame = ttk.Frame(self)
            text_frame.pack(fill=BOTH, expand=True)
            
            # 日誌文本框
            self.log_text = tk.Text(
                text_frame,
                height=8,
                wrap=tk.WORD,
                font=("Noto Sans TC", 10),
                bg="#343a40",  # 使用深色背景
                fg="#f8f9fa",  # 使用淺色文字
                insertbackground="#f8f9fa"  # 光標顏色
            )
            self.log_text.pack(side=LEFT, fill=BOTH, expand=True)
            
            # 滾動條
            scrollbar = ttk.Scrollbar(text_frame, orient=VERTICAL, command=self.log_text.yview)
            self.log_text.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=RIGHT, fill=Y)
            
            # 綁定變更事件
            self.device_var.trace("w", self.on_filter_changed)
            self.level_var.trace("w", self.on_filter_changed)
            
            # 綁定滾輪事件
            self.log_text.bind("<MouseWheel>", self.on_mouse_wheel)
            
            # 設置標籤
            self.tag_config()
            
            # 設置自動刷新
            self.setup_auto_refresh()
            
            log_manager.debug("日誌顯示界面UI初始化完成")
            
        except Exception as e:
            log_manager.error(f"初始化日誌顯示界面時發生錯誤: {str(e)}")
            raise
            
    def on_mouse_wheel(self, event):
        """處理滾輪事件"""
        # 當用戶手動滾動時，暫時禁用自動滾動
        if event.delta < 0:  # 向下滾動
            if self.log_text.yview()[1] < 1:  # 不在底部
                self.auto_scroll = False
        else:  # 向上滾動
            self.auto_scroll = False
            
    def tag_config(self):
        """配置日誌級別的顯示樣式"""
        self.log_text.tag_configure("DEBUG", foreground="#808080")  # 灰色
        self.log_text.tag_configure("INFO", foreground="#FFFFFF")   # 白色
        self.log_text.tag_configure("WARNING", foreground="#FFA500")  # 橙色
        self.log_text.tag_configure("ERROR", foreground="#FF0000")    # 紅色
        self.log_text.tag_configure("CRITICAL", foreground="#FF0000", underline=1)  # 紅色加下劃線
            
    def setup_auto_refresh(self):
        """設置自動刷新"""
        def check_auto_refresh():
            if self.auto_refresh_var.get():
                self.refresh_logs(auto=True)
            self.after(1000, check_auto_refresh)  # 每秒檢查一次
            
        check_auto_refresh()
            
    def update_devices(self, devices):
        """更新設備列表"""
        self.devices = devices  # 保存設備列表
        
        # 更新下拉選單選項
        current_device = self.device_var.get()
        device_list = ["全部", "system"]
        
        # 添加設備名稱和ID的映射
        for device in devices:
            device_name = f"{device['device_name']} ({device['id']}) - {device['bank_account']}"
            device_list.append(device_name)
        
        # 更新下拉選單
        self.device_combo["values"] = device_list
        
        # 如果當前選擇的設備不在新列表中，重置為"全部"
        if current_device not in device_list:
            self.device_var.set("全部")
            
    def refresh_logs(self, auto=False):
        """刷新日誌顯示"""
        try:
            # 獲取過濾條件
            selected_level = self.level_var.get()
            selected_device = self.device_var.get()
            
            # 轉換設備選擇為日誌格式
            if selected_device == "全部":
                device_filter = None
            elif selected_device == "system":
                device_filter = "system"
            else:
                # 從選擇的設備名稱中提取設備ID
                # 使用與 update_devices 中相同的格式進行比較
                device = next(
                    (d for d in self.devices if f"{d['device_name']} ({d['id']}) - {d['bank_account']}" == selected_device),
                    None
                )
                if device:
                    device_filter = device["id"]
                else:
                    device_filter = selected_device
            
            # 讀取日誌
            logs = log_manager.read_logs(
                level=selected_level if selected_level != "ALL" else None,
                device=device_filter
            )
            
            # 檢查日誌數量是否變化
            if auto and len(logs) == self.last_log_count:
                return
                
            # 保存當前滾動位置
            current_first = self.log_text.yview()[0]
            
            # 清空文本框
            self.log_text.delete(1.0, tk.END)
            
            # 添加日誌
            for log in logs:
                # 獲取日誌級別
                parts = log.split(' - ')
                if len(parts) >= 2:
                    level = parts[1].strip()
                    self.log_text.insert(tk.END, log + "\n", level)
                else:
                    self.log_text.insert(tk.END, log + "\n")
                    
            # 更新日誌計數
            self.last_log_count = len(logs)
            
            # 處理滾動
            if self.auto_scroll_var.get() and (auto or self.auto_scroll):
                self.log_text.see(tk.END)
                self.auto_scroll = True
            else:
                self.log_text.yview_moveto(current_first)
                
        except Exception as e:
            print(f"刷新日誌時發生錯誤: {str(e)}")
            
    def on_filter_changed(self, *args):
        """過濾條件變更時更新顯示"""
        self.refresh_logs()
            
    def clear_display(self):
        """清除顯示"""
        self.log_text.delete(1.0, tk.END)
        self.last_log_count = 0
        self.auto_scroll = True
        
    def add_log(self, message, level="INFO"):
        """添加日誌"""
        log_manager.log(level, message)
        self.refresh_logs()
