import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import requests
from gui.utils.logger import log_manager
from gui.utils.config import config_manager
from gui.utils.dialog_utils import DialogBase
from bank import bank_registry

class AddDeviceDialog(DialogBase):
    def __init__(self, parent, edit_data=None):
        self.edit_data = edit_data
        self.is_edit_mode = edit_data is not None
        
        super().__init__(
            parent,
            "編輯設備" if self.is_edit_mode else "新增設備",
            min_width=400,
            min_height=400  # 降低最小高度
        )
        
        # 設置對話框位置
        window_width = self.winfo_toplevel().winfo_width()
        window_height = self.winfo_toplevel().winfo_height()
        dialog_x = max(0, (window_width - 400) // 2)  # 對話框寬度為 400
        dialog_y = max(0, (window_height - 400) // 2)  # 對話框高度為 400
        self.geometry(f"+{dialog_x}+{dialog_y}")
        
    def _init_ui(self):
        """初始化對話框UI"""
        # 主容器使用卡片式設計
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # 標題
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=X, pady=(0,20))
        
        ttk.Label(
            title_frame,
            text="編輯設備" if self.is_edit_mode else "新增設備",
            font=("Noto Sans TC", 18, "bold"),
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # 分隔線
        ttk.Separator(main_frame).pack(fill=X, pady=(0,20))
        
        # 表單容器
        # 創建可滾動的容器
        container = ttk.Frame(main_frame)
        container.pack(fill=BOTH, expand=True, padx=(0, 5))  # 右側留出空間給滾動條
        
        # 創建 Canvas
        canvas = tk.Canvas(container, highlightthickness=0)  # 移除 Canvas 的邊框
        scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview, bootstyle="round")  # 使用圓角風格
        
        # 創建內容框架
        form_frame = ttk.Frame(canvas)
        form_frame.pack(fill=BOTH, expand=True)
        
        # 配置 Canvas 滾動
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 創建 Canvas 視窗
        canvas.create_window((0, 0), window=form_frame, anchor='nw', tags='form_frame')
        
        # 配置滾動區域
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox('all'))
        form_frame.bind('<Configure>', configure_scroll_region)
        
        # 配置 Canvas 大小
        def configure_canvas(event):
            canvas.itemconfig('form_frame', width=event.width)
        canvas.bind('<Configure>', configure_canvas)
        
        # 放置 Canvas 和 Scrollbar
        canvas.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 10))  # Canvas 右側加上間距
        scrollbar.pack(side=RIGHT, fill=Y, padx=(0, 5))  # Scrollbar 右側也加上間距
        
        # 綁定滾輪事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # 解除綁定的方法
        def _unbind_mousewheel():
            canvas.unbind_all("<MouseWheel>")
        self.protocol("WM_DELETE_WINDOW", lambda: [_unbind_mousewheel(), self.destroy()])
        
        # 設備名稱
        ttk.Label(
            form_frame,
            text="設備名稱",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.name_var = tk.StringVar()
        ttk.Entry(
            form_frame,
            textvariable=self.name_var,
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        ).pack(fill=X, pady=(0,15))
        
        # 銀行選擇（編輯模式下禁用）
        ttk.Label(
            form_frame,
            text="銀行",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.bank_var = tk.StringVar()
        ttk.Combobox(
            form_frame,
            textvariable=self.bank_var,
            values=bank_registry.get_bank_names(),  # 使用 bank_registry 獲取銀行列表
            state="readonly" if not self.is_edit_mode else "disabled",
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        ).pack(fill=X, pady=(0,15))
        
        # 帳戶類型
        ttk.Label(
            form_frame,
            text="帳戶類型",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.account_type_var = tk.StringVar()
        account_type_combo = ttk.Combobox(
            form_frame,
            textvariable=self.account_type_var,
            values=["個人", "公司"],
            state="readonly",
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        )
        account_type_combo.pack(fill=X, pady=(0,15))
        account_type_combo.set("個人")  # 預設選擇個人帳戶
        
        # 帳號
        ttk.Label(
            form_frame,
            text="帳號",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.account_var = tk.StringVar()
        ttk.Entry(
            form_frame,
            textvariable=self.account_var,
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        ).pack(fill=X, pady=(0,15))
        
        # 收款人
        ttk.Label(
            form_frame,
            text="收款人",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.receiver_var = tk.StringVar()
        ttk.Entry(
            form_frame,
            textvariable=self.receiver_var,
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        ).pack(fill=X, pady=(0,15))
        
        # 身分證字號
        ttk.Label(
            form_frame,
            text="身分證字號",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.id_number_var = tk.StringVar()
        ttk.Entry(
            form_frame,
            textvariable=self.id_number_var,
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        ).pack(fill=X, pady=(0,15))
        
        # 使用者代號
        ttk.Label(
            form_frame,
            text="使用者代號",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        self.user_id_var = tk.StringVar()
        ttk.Entry(
            form_frame,
            textvariable=self.user_id_var,
            font=("Noto Sans TC", 11),
            bootstyle="primary"
        ).pack(fill=X, pady=(0,15))
        
        # 使用者密碼
        ttk.Label(
            form_frame,
            text="使用者密碼",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(0,5))
        
        # 密碼輸入區域容器
        password_frame = ttk.Frame(form_frame)
        password_frame.pack(fill=X, pady=(0,15))
        
        self.user_password_var = tk.StringVar()
        self.password_entry = ttk.Entry(
            password_frame,
            textvariable=self.user_password_var,
            font=("Noto Sans TC", 11),
            bootstyle="primary",
            show="*"
        )
        self.password_entry.pack(side=LEFT, fill=X, expand=True)
        
        # 顯示/隱藏密碼按鈕
        self.show_password = False
        self.toggle_password_btn = ttk.Button(
            password_frame,
            text="👁" if not self.show_password else "👁‍🗨",  # 使用不同的眼睛符號表示狀態
            width=3,
            bootstyle="link-primary",  # 使用主色調
            cursor="hand2",  # 使用手型游標
            command=self._toggle_password_visibility
        )
        self.toggle_password_btn.pack(side=RIGHT, padx=(5, 0))

        # 使用代理勾選框
        ttk.Label(
            form_frame,
            text="代理設定",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        ).pack(anchor=W, pady=(15,5)) # 增加上方間距

        self.use_proxy_var = tk.BooleanVar()
        ttk.Checkbutton(
            form_frame,
            text="啟用此設備的代理伺服器",
            variable=self.use_proxy_var,
            bootstyle="primary-round-toggle" # 使用圓形切換按鈕樣式
        ).pack(anchor=W, pady=(0,15))
        
        # 分隔線
        ttk.Separator(main_frame).pack(fill=X, pady=20)
        
        # 按鈕容器
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X)
        
        # 取消按鈕
        ttk.Button(
            button_frame,
            text="取消",
            command=self.destroy,
            bootstyle="secondary",
            width=15
        ).pack(side=RIGHT)
        
        # 確定按鈕
        ttk.Button(
            button_frame,
            text="儲存" if self.is_edit_mode else "新增設備",
            command=self.on_confirm,
            bootstyle="primary",
            width=15
        ).pack(side=RIGHT, padx=10)
        
        # 如果是編輯模式，填入現有數據
        if self.is_edit_mode:
            self.name_var.set(self.edit_data["device_name"])
            bank_info = bank_registry.get_bank_by_code(self.edit_data["bank_code"])
            if bank_info:
                self.bank_var.set(bank_info.name)
            self.account_var.set(self.edit_data["bank_account"])
            self.receiver_var.set(self.edit_data["account_name"])  # 收款人是必填欄位
            # 設置帳戶類型
            account_type = self.edit_data.get("account_type", 1)
            self.account_type_var.set("個人" if account_type == 1 else "公司")
            # 其他欄位是選填的
            self.user_id_var.set(self.edit_data.get("bank_user_id", ""))
            self.user_password_var.set(self.edit_data.get("bank_user_password", ""))
            self.id_number_var.set(self.edit_data.get("bank_id_number", ""))
            # 載入現有的代理設定
            self.use_proxy_var.set(self.edit_data.get("use_proxy", False))
            
    def _toggle_password_visibility(self):
        """切換密碼顯示/隱藏"""
        self.show_password = not self.show_password
        self.password_entry.configure(show="" if self.show_password else "*")
        self.toggle_password_btn.configure(
            text="👁‍🗨" if self.show_password else "👁",
            bootstyle="link-danger" if self.show_password else "link-primary"
        )
        
    def on_confirm(self):
        """處理新增或編輯設備"""
        action = "編輯" if self.is_edit_mode else "新增"
        
        try:
            # 獲取表單數據
            name = self.name_var.get()
            bank_name = self.bank_var.get()
            account = self.account_var.get()
            receiver = self.receiver_var.get()
            
            # 檢查必填欄位
            if not all([name, bank_name, account, receiver]):
                log_manager.warning(f"{action}設備失敗：必填欄位未填寫完整")
                Messagebox.show_error("請填寫必填欄位（設備名稱、銀行、帳號、收款人）", "錯誤")
                return
                
            # 使用 bank_registry 獲取銀行資訊
            bank_info = bank_registry.get_bank_by_name(bank_name)
            if not bank_info:
                log_manager.error(f"{action}設備失敗：找不到銀行資訊 {bank_name}")
                Messagebox.show_error("無效的銀行選擇", "錯誤")
                return
                
            log_manager.info(f"嘗試{action}設備：{name} ({bank_name})")
            
            # 準備請求數據
            request_data = {
                "device_name": name,
                "bank_account": account,
                "account_name": receiver,
                "bank_user_id": self.user_id_var.get(),
                "bank_user_password": self.user_password_var.get(),
                "bank_id_number": self.id_number_var.get(),
                "account_type": 1 if self.account_type_var.get() == "個人" else 2,
                "use_proxy": self.use_proxy_var.get() # 加入代理設定
            }
            
            # 如果不是編輯模式，添加銀行資訊
            if not self.is_edit_mode:
                request_data.update({
                    "bank_code": bank_info.code,
                    "bank_name": bank_info.name
                })
            
            # 使用父元件的 device_manager 處理請求
            if not hasattr(self.parent, 'device_manager'):
                raise Exception("找不到設備管理器")
                
            if self.is_edit_mode:
                self.parent.device_manager.update_device(self.edit_data['id'], request_data)
            else:
                self.parent.device_manager.add_device(request_data)
                
            log_manager.info(f"{action}設備成功")
            self.parent.refresh_devices()
            self.destroy()
            
        except Exception as e:
            error_message = str(e)
            log_manager.error(f"{action}設備失敗：{error_message}")
            Messagebox.show_error(error_message, "錯誤")
