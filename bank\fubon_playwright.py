# -*- coding: utf-8 -*-
"""
富邦銀行 Playwright 自動化模組
"""

import time
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError
from .base_playwright import BankBase
from gui.utils.logger import log_manager

class FubonBank(BankBase):
    """富邦銀行 Playwright 操作類"""

    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=False, use_proxy: bool = False):
        # 富邦銀行代碼為 012，預設使用電腦版網頁
        super().__init__(bank_code='012', bank_account=bank_account, device_id=device_id, device_data=device_data, is_mobile=is_mobile, use_proxy=use_proxy)
        self.login_url = "https://ebank.taipeifubon.com.tw/B2C/common/Index.faces"
        self.cookies_updated = False # 追蹤 Cookie 是否已成功更新過
        self.last_cookie_update_time = 0 # 上次更新 Cookie 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）

    # --- Helper Methods ---
    def _get_frame1(self, timeout=30000):
        """安全地獲取主頁面的 frame1"""
        try:
            frame1_locator = self.page.locator("#frame1")
            frame1_locator.wait_for(state="visible", timeout=timeout)
            return frame1_locator.content_frame
        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', f"等待 frame1 超時 ({timeout}ms)", "frame1_timeout")
            return None
        except Exception as e:
            self.log_with_screenshot('error', f"獲取 frame1 時發生錯誤: {e}", "frame1_error")
            return None

    def _get_txn_frame(self, frame1, timeout=30000):
        """安全地獲取 frame1 內的 txnFrame"""
        if not frame1:
            log_manager.error("無法獲取 txnFrame，因為 frame1 為空", device=self.device_id)
            return None
        try:
            txn_frame_locator = frame1.locator("iframe[name=\"txnFrame\"]")
            txn_frame_locator.wait_for(state="visible", timeout=timeout)
            return txn_frame_locator.content_frame
        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', f"等待 txnFrame 超時 ({timeout}ms)", "txnframe_timeout")
            return None
        except Exception as e:
            self.log_with_screenshot('error', f"獲取 txnFrame 時發生錯誤: {e}", "txnframe_error")
            return None

    # --- Login Flow Steps ---
    def _click_login_button(self, frame1):
        """點擊登入按鈕"""
        if not frame1: return False
        try:
            login_button = frame1.get_by_role("link", name="登入")
            login_button.wait_for(state="visible", timeout=30000)
            login_button.click(timeout=10000)
            log_manager.info("已點擊登入按鈕，等待 txnFrame 載入登入表單...", device=self.device_id)
            self.update_status("點擊登入按鈕")
            # 短暫等待，讓 iframe 有時間開始載入
            self.page.wait_for_timeout(2000)
            return True
        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', "點擊登入按鈕超時", "login_button_timeout")
            return False
        except Exception as e:
            self.log_with_screenshot('error', f"點擊登入按鈕時發生錯誤: {e}", "login_button_error")
            return False

    def _wait_for_login_form_in_txnframe(self, txn_frame):
        """在 txnFrame 中等待登入表單元素出現"""
        if not txn_frame: return False
        try:
            # 等待 "身分證字號" 標籤旁邊的 input 元素出現
            # 使用 XPath 相對定位器，並選取第一個匹配項 [1]
            id_input_locator = txn_frame.locator('(//td[contains(@class, "hd") and contains(text(), "身分證字號")]/following-sibling::td//input[contains(@class, "input_txt")])[1]')
            # 增加等待時間至 30 秒
            id_input_locator.wait_for(state="visible", timeout=30000)
            log_manager.info("登入表單輸入框 (身分證字號) 在 txnFrame 中已可見", device=self.device_id)
            return True
        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', "在 txnFrame 中等待登入輸入框 (身分證字號) 超時", "login_input_txnframe_timeout")
            return False
        except Exception as e:
             self.log_with_screenshot('error', f"在 txnFrame 中等待登入表單時發生錯誤: {e}", "login_form_txnframe_error")
             return False

    def _auto_fill_login_form(self, txn_frame):
        """在 txnFrame 中自動填入登入表單"""
        if not txn_frame: return False
        log_manager.info("嘗試在 txnFrame 中自動填入登入資訊 (使用相對定位器)", device=self.device_id)
        try:
            # 使用相對定位器找到輸入框，並選取第一個匹配項 [1]
            id_input = txn_frame.locator('(//td[contains(@class, "hd") and contains(text(), "身分證字號")]/following-sibling::td//input[contains(@class, "input_txt")])[1]')
            user_code_input = txn_frame.locator('(//td[contains(@class, "hd") and contains(text(), "使用者代碼")]/following-sibling::td//input[contains(@class, "input_txt")])[1]')
            password_input = txn_frame.locator('(//td[contains(@class, "hd") and contains(text(), "使用者密碼")]/following-sibling::td//input[contains(@class, "input_txt")])[1]') # 注意：動態鍵盤可能影響此處

            # 檢查必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.warning(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False

            # 等待輸入框可見
            id_input.wait_for(state="visible", timeout=10000)
            user_code_input.wait_for(state="visible", timeout=10000)
            password_input.wait_for(state="visible", timeout=10000)

            # 填入資料
            id_input.fill(self.device_data["bank_id_number"], timeout=5000)
            user_code_input.fill(self.device_data["bank_user_id"], timeout=5000)
            # 直接填入密碼，如果動態鍵盤阻止，此處會失敗
            password_input.fill(self.device_data["bank_user_password"], timeout=5000)
            log_manager.info("已自動填入登入資訊", device=self.device_id)

            # 嘗試自動識別並填入驗證碼 - 使用統一接口
            # 富邦銀行專用選擇器
            captcha_selectors = ['#m1_captchaImage']
            input_selectors = ['#m1_userCaptcha']

            if self.handle_captcha_ocr(txn_frame, captcha_selectors, input_selectors):
                log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                self.update_status("已自動填入(含驗證碼)")
            else:
                log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                self.update_status("已自動填入(需手動驗證碼)")

            return True
        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', "自動填入登入資訊超時", "autofill_timeout")
            return False
        except Exception as e:
            self.log_with_screenshot('error', f"自動填入時發生錯誤: {e}", "autofill_error")
            return False



    def _wait_for_manual_login(self, frame1):
        """等待使用者手動輸入驗證碼並登入成功 (檢查 '您上次登入系統時間為：')"""
        if not frame1: return False
        log_manager.info("請手動輸入驗證碼並完成登入...", device=self.device_id)
        self.update_status("等待手動登入")
        while self.is_running: # 在循環中檢查停止標誌
            try:
                # 嘗試獲取 txnFrame，使用較短超時
                txn_frame = self._get_txn_frame(frame1, timeout=5000)
                if not txn_frame:
                    time.sleep(2)
                    continue

                # 檢查包含 "您上次登入系統時間為：" 的文字是否可見，使用短超時
                login_time_indicator = txn_frame.locator('//*[contains(text(), "您上次登入系統時間為：")]')
                try:
                    if login_time_indicator.is_visible(timeout=1000): # 檢查 1 秒
                        log_manager.info("偵測到登入成功 (找到 '您上次登入系統時間為：')", device=self.device_id)
                        self.update_status("登入成功")
                        # 找到文字表示登入成功，直接返回 True
                        return True # 找到文字表示登入成功
                except PlaywrightTimeoutError:
                    # 目標文字還不可見，繼續循環等待
                    pass

            except Exception as e:
                # 記錄檢查過程中的錯誤，但繼續等待，除非被停止
                if self.is_running:
                    log_manager.warning(f"等待登入成功時發生檢查錯誤: {e}", device=self.device_id)

            # 在每次循環檢查之間短暫休眠
            time.sleep(2)

        # 如果循環是因為 self.is_running 變為 False 而退出
        log_manager.info("等待手動登入被中斷 (is_running is False)", device=self.device_id)
        return False # 返回 False 表示未成功登入（因為被停止）

    def _navigate_to_deposit_page(self, txn_frame):
        """點擊 '我的存款 NTD' 連結導航至存款頁面 (在 txnFrame 內操作，嘗試 hover)"""
        if not txn_frame: return False
        try:
            # 1. 定位用於 hover 的容器 (#CDS01_Btn_Icon) 和要點擊的連結
            deposit_container_locator = txn_frame.locator('#CDS01_Btn_Icon')
            # 連結定位器保持不變，因為它在容器內部
            deposit_link_locator = deposit_container_locator.get_by_role("link", name="我的存款") # 更新連結名稱為 "我的存款"

            # 等待容器和連結附加到 DOM (不一定可見)
            deposit_container_locator.wait_for(state="attached", timeout=30000)
            deposit_link_locator.wait_for(state="attached", timeout=30000)
            log_manager.info("找到 '我的存款 NTD' 容器和連結元素", device=self.device_id)

            # 2. 嘗試模擬懸停 (Hover) 到容器上，這可能會使連結可見或可點擊
            try:
                log_manager.debug("嘗試懸停到 #CDS01_Btn_Icon 容器...", device=self.device_id)
                deposit_container_locator.hover(timeout=10000)
                log_manager.info("已懸停到 #CDS01_Btn_Icon 容器", device=self.device_id)
                # 短暫等待，讓可能的 CSS 過渡或 JS 生效
                self.page.wait_for_timeout(500)
            except PlaywrightTimeoutError:
                 log_manager.warning("懸停到 #CDS01_Btn_Icon 容器超時，仍嘗試點擊連結", device=self.device_id)
            except Exception as hover_error:
                 log_manager.warning(f"懸停時發生錯誤: {hover_error}，仍嘗試點擊連結", device=self.device_id)


            # 3. 等待連結可見並點擊
            log_manager.debug("等待 '我的存款' 連結可見...", device=self.device_id)
            deposit_link_locator.wait_for(state="visible", timeout=15000) # 懸停後等待可見
            log_manager.info("找到 '我的存款' 連結，準備點擊...", device=self.device_id)
            deposit_link_locator.click(timeout=15000)
            log_manager.info("已點擊 '我的存款' 連結", device=self.device_id)

            # 4. 等待 txnFrame 內容更新 (等待 '帳號' 欄位出現作為標誌)
            try:
                account_header = txn_frame.get_by_text("帳號", exact=True)
                account_header.wait_for(state="visible", timeout=30000)
                log_manager.info("偵測到存款頁面內容已載入 (找到 '帳號' 標頭)", device=self.device_id)
            except PlaywrightTimeoutError:
                log_manager.warning("未找到 '帳號' 標頭，嘗試等待 networkidle...", device=self.device_id)
                try:
                    txn_frame.page.wait_for_load_state("networkidle", timeout=20000)
                    log_manager.info("txnFrame networkidle 完成", device=self.device_id)
                except PlaywrightTimeoutError:
                     self.log_with_screenshot('warning', "等待存款頁面標誌或 networkidle 超時", "deposit_page_load_timeout")

            self.update_status("進入存款頁面")
            return True

        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', "定位/懸停/點擊 '我的存款 NTD' 或等待頁面加載超時", "deposit_navigate_timeout")
            return False
        except Exception as e:
            self.log_with_screenshot('error', f"導航至存款頁面時發生錯誤: {e}", "deposit_navigate_error")
            return False

    def _verify_account(self, txn_frame):
        """驗證帳號是否存在 (在 txnFrame 中，優先檢查 cell，其次 link，重試 3 次，間隔 5 秒)"""
        log_manager.debug("進入 _verify_account 函數 (在 txnFrame 中)", device=self.device_id)
        if not txn_frame:
            log_manager.error("_verify_account 收到無效的 txn_frame", device=self.device_id)
            return False

        account_str = str(self.bank_account)
        max_retries = 3
        retry_delay = 5

        for attempt in range(max_retries):
            if not self.is_running: return False # 每次重試前檢查是否已停止

            locator_found = False
            try:
                # 嘗試定位 cell
                try:
                    log_manager.debug(f"第 {attempt + 1}/{max_retries} 次嘗試在 txnFrame 中定位帳號 cell (get_by_role): {account_str}", device=self.device_id)
                    account_locator = txn_frame.get_by_role("cell", name=account_str)
                    log_manager.debug(f"帳號 cell 定位器: {account_locator}", device=self.device_id)
                    account_locator.wait_for(state="visible", timeout=5000) # 縮短 cell 檢查超時
                    locator_found = True
                    log_manager.info(f"帳號比對成功 (找到 cell): {account_str}", device=self.device_id)
                except PlaywrightTimeoutError:
                    log_manager.debug(f"第 {attempt + 1} 次嘗試未找到帳號 cell，嘗試定位 link...", device=self.device_id)
                    # 嘗試定位 link
                    try:
                        log_manager.debug(f"第 {attempt + 1}/{max_retries} 次嘗試在 txnFrame 中定位帳號連結 (get_by_role): {account_str}", device=self.device_id)
                        account_locator = txn_frame.get_by_role("link", name=account_str)
                        log_manager.debug(f"帳號 link 定位器: {account_locator}", device=self.device_id)
                        account_locator.wait_for(state="visible", timeout=10000) # link 檢查超時維持 10 秒
                        locator_found = True
                        log_manager.info(f"帳號比對成功 (找到 link): {account_str}", device=self.device_id)
                    except PlaywrightTimeoutError:
                        log_manager.warning(f"第 {attempt + 1} 次嘗試未找到帳號 cell 或 link。", device=self.device_id)
                        # 兩種定位器都超時，locator_found 保持 False

                # 檢查是否找到
                if locator_found:
                    self.update_status("帳號比對成功")
                    return True # 找到即返回成功

                # 如果未找到 (locator_found is False)
                if attempt < max_retries - 1:
                    log_manager.info(f"等待 {retry_delay} 秒後重試...", device=self.device_id)
                    # 使用 time.sleep() 進行等待，同時檢查 is_running
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                else:
                    # 最後一次嘗試失敗
                    self.log_with_screenshot('error', f"帳號比對失敗：重試 {max_retries} 次後仍未找到帳號 {account_str}", "account_mismatch_retries_failed")
                    self.update_status("錯誤：帳號不符")
                    self.is_running = False # 設置停止標誌
                    return False # 返回 False，讓主循環處理退出

            except Exception as e:
                self.log_with_screenshot('error', f"驗證帳號時發生錯誤 (嘗試 {attempt + 1}): {e}", "account_verify_error")
                # 發生意外錯誤，處理重試或停止邏輯
                if attempt < max_retries - 1:
                    log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                else:
                    self.is_running = False # 最後一次嘗試也出錯，則停止
                    return False

        # 理論上不會執行到這裡，因為循環內會 return 或 raise
        return False

    def _navigate_to_transaction_details(self, txn_frame):
        """導航至交易明細查詢頁面 (在 txnFrame 中)"""
        if not txn_frame: return False
        try:
            # 在 txnFrame 中查找元素
            quick_func_link = txn_frame.get_by_role("link", name="快速功能")
            quick_func_link.wait_for(state="visible", timeout=30000)
            quick_func_link.click(timeout=10000)
            log_manager.info("已點擊 '快速功能'", device=self.device_id)
            self.page.wait_for_timeout(1000) # 短暫等待

            trans_detail_link = txn_frame.get_by_role("link", name="交易明細查詢")
            trans_detail_link.wait_for(state="visible", timeout=30000)
            trans_detail_link.click(timeout=10000)
            log_manager.info("已點擊 '交易明細查詢'", device=self.device_id)
            self.update_status("進入交易明細")

            # 第一次進入後點擊 "開始查詢"
            log_manager.info("嘗試在導航後點擊 '開始查詢'", device=self.device_id)
            if not self._click_start_query_button(txn_frame):
                log_manager.warning("導航後點擊 '開始查詢' 失敗，但繼續執行", device=self.device_id)
                # 根據需求，這裡可以決定是否要返回 False
                # return False
            else:
                log_manager.info("導航後成功點擊 '開始查詢'", device=self.device_id)
                # 等待查詢結果載入 (可選，根據實際情況調整等待時間或條件)
                self.page.wait_for_timeout(3000) # 等待 3 秒讓查詢結果可能載入

            return True
        except PlaywrightTimeoutError:
            self.log_with_screenshot('error', "點擊 '快速功能'/'交易明細查詢' 或等待特定元素超時", "quick_func_timeout")
            return False
        except Exception as e:
            self.log_with_screenshot('error', f"點擊 '快速功能' 或 '交易明細查詢' 時發生錯誤: {e}", "quick_func_error")
            return False

    def _get_and_update_cookies(self):
        """獲取並更新 Cookie"""
        if not self.is_running: return False
        try:
            # 獲取 cookies 列表
            cookies_list = self.context.cookies()

            # 將 cookies 列表轉換為 "name=value; name=value; ..." 格式的字串
            cookie_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies_list])

            # 構建成目標格式的字典
            formatted_cookies = {"Cookie": cookie_string}
            log_manager.debug(f"格式化後的 Cookie 字串: {cookie_string[:100]}...", device=self.device_id) # 只記錄前 100 個字符

            # 使用格式化後的字典調用 update_cookies
            if self.update_cookies(formatted_cookies):
                log_manager.debug("成功更新 Cookie 到 API", device=self.device_id)
                self.cookies_updated = True
                self.last_cookie_update_time = time.time()
                self.update_status("已更新 Cookie")
                return True
            else:
                log_manager.warning("更新 Cookie 到 API 失敗", device=self.device_id)
                return False
        except Exception as e:
            self.log_with_screenshot('error', f"獲取或更新 Cookie 時發生錯誤: {e}", "cookie_error")
            return False

    def _click_start_query_button(self, txn_frame):
        """點擊交易明細頁面中的 '開始查詢' 按鈕 (在 txnFrame 中)"""
        if not self.is_running: return False
        if not txn_frame:
            log_manager.error("無法點擊 '開始查詢'，因為 txn_frame 為空", device=self.device_id)
            return False
        try:
            start_query_button = txn_frame.get_by_role("link", name="開始查詢")
            start_query_button.wait_for(state="visible", timeout=30000)
            log_manager.debug("找到 '開始查詢' 按鈕，準備點擊...", device=self.device_id)
            start_query_button.click(timeout=10000)
            log_manager.info("已點擊 '開始查詢' 按鈕", device=self.device_id)
            # 可選：增加等待，確保查詢動作已觸發或頁面開始更新
            self.page.wait_for_timeout(2000)
            return True
        except PlaywrightTimeoutError:
            self.log_with_screenshot('warning', "點擊 '開始查詢' 按鈕超時", "start_query_timeout")
            return False
        except Exception as e:
            self.log_with_screenshot('warning', f"點擊 '開始查詢' 按鈕時發生錯誤: {e}", "start_query_error")
            return False

    # --- Keep Alive Logic ---
    def _check_and_handle_timeout_warning(self):
        """檢查並處理超時警告彈窗"""
        if not self.is_running: return
        try:
            frame1 = self._get_frame1(timeout=5000) # 使用較短超時檢查
            if not frame1: return # 如果 frame1 快速檢查失敗，則可能頁面已登出或錯誤

            timeout_warning = frame1.locator("#timeout_warning").get_by_text("系統訊息")

            # 使用 is_visible() 進行非阻塞檢查
            if timeout_warning.is_visible(timeout=1000): # 短暫檢查
                log_manager.info("偵測到超時警告彈窗", device=self.device_id)
                continue_button = frame1.get_by_role("link", name="繼續使用")
                if continue_button.is_visible(timeout=5000):
                    continue_button.click(timeout=5000)
                    log_manager.info("已點擊 '繼續使用' 按鈕", device=self.device_id)
                    self.update_status("保持連線")
                    # 點擊後可能需要短暫等待頁面反應
                    self.page.wait_for_timeout(2000)
                else:
                    log_manager.warning("找到超時警告但未找到 '繼續使用' 按鈕", device=self.device_id)
            # else:
                # log_manager.debug("未偵測到超時警告", device=self.device_id) # 可選：用於調試

        except PlaywrightTimeoutError:
            # is_visible 超時是正常的，表示元素未出現
            pass
        except Exception as e:
            # 捕獲檢查彈窗時的其他錯誤
            if self.is_running: # 僅在運行時記錄
                log_manager.warning(f"檢查超時彈窗時發生錯誤: {e}", device=self.device_id)
                # 這裡不截圖，避免過多日誌

    # --- Main Execution Flow ---
    def start(self):
        """開始執行銀行登入和監控流程"""
        self.is_running = True
        log_manager.info(f"開始執行富邦銀行 ({self.bank_account}) 操作", device=self.device_id)

        try:
            self.setup_browser()
            if not self.page:
                log_manager.error("瀏覽器頁面未能成功初始化", device=self.device_id)
                self.is_running = False # 設置停止標誌
                return # 直接返回，後續清理會在線程結束時執行

            # --- 主要執行循環 ---
            while self.is_running:
                try:
                    # --- 登入流程 ---
                    self.update_status("正在開啟富邦銀行網頁...")
                    self.page.goto(self.login_url, timeout=60000, wait_until="networkidle")
                    log_manager.info("成功導航至富邦銀行登入頁面", device=self.device_id)
                    if not self.is_running: break

                    frame1 = self._get_frame1()
                    if not frame1 or not self.is_running: break

                    if not self._click_login_button(frame1) or not self.is_running: break
                    txn_frame_login = self._get_txn_frame(frame1)
                    if not txn_frame_login or not self.is_running: break
                    if not self._wait_for_login_form_in_txnframe(txn_frame_login) or not self.is_running: break

                    self.check_auto_fill_settings()
                    if self.should_auto_fill:
                        if not self._auto_fill_login_form(txn_frame_login):
                            log_manager.warning("自動填入失敗，請手動登入", device=self.device_id)
                    # 不需要 else，因為 _wait_for_manual_login 會處理等待
                    self.update_status("等待手動登入")

                    if not self._wait_for_manual_login(frame1): # 傳入 frame1
                         # 如果返回 False 是因為 is_running 變為 False，外層循環會處理
                         break
                    # --- 登入後操作 ---
                    # _wait_for_manual_login 成功後，我們知道已登入
                    # 1. 導航至 '我的存款 NTD' (使用 page.goto)
                    txn_frame_after_login = self._get_txn_frame(frame1, timeout=60000) # 獲取登入後的 txnFrame
                    if not txn_frame_after_login or not self.is_running: break
                    if not self._navigate_to_deposit_page(txn_frame_after_login):
                         self.is_running = False # 如果導航失敗，停止
                         break
                    if not self.is_running: break

                    # 2. 驗證帳號 (在 page.goto 後的新頁面和 frame 中進行)
                    log_manager.debug("使用 page.goto 導航至存款頁面後，重新獲取 frame...", device=self.device_id)
                    # 增加等待時間並加入健壯性檢查
                    frame1_after_deposit = self._get_frame1(timeout=90000) # 增加 frame1 等待時間
                    if not frame1_after_deposit or not self.is_running:
                        log_manager.error("重新獲取 frame1 失敗或超時", device=self.device_id)
                        break
                    # 等待 txnFrame 載入，可以等待特定內容或 URL
                    txn_frame_after_deposit = self._get_txn_frame(frame1_after_deposit, timeout=90000) # 增加 txnFrame 等待時間
                    if not txn_frame_after_deposit or not self.is_running:
                        log_manager.error("重新獲取 txnFrame 失敗或超時", device=self.device_id)
                        break
                    # 可選：添加額外等待，確保 txnFrame 內容穩定
                    try:
                        txn_frame_after_deposit.get_by_text("帳號", exact=True).wait_for(state="visible", timeout=15000)
                        log_manager.debug("在重新獲取的 txnFrame 中找到 '帳號' 標頭", device=self.device_id)
                    except PlaywrightTimeoutError:
                        log_manager.warning("重新獲取 txnFrame 後未立即找到 '帳號' 標頭，繼續執行", device=self.device_id)


                    if not self._verify_account(txn_frame_after_deposit): # 傳遞新的 txn_frame
                         # verify_account 內部設置 is_running=False
                         break
                    if not self.is_running: break

                    # 3. 導航至交易明細 (在驗證帳號後的同一個 txn_frame 中進行)
                    if not self._navigate_to_transaction_details(txn_frame_after_login): # 傳遞同一個 txn_frame
                         self.is_running = False
                         break
                    if not self.is_running: break

                    # 4. 獲取並更新 Cookie
                    if not self._get_and_update_cookies():
                        log_manager.error("首次獲取或更新 Cookie 失敗", device=self.device_id)
                        # 選擇繼續，但可能影響後續操作

                    # --- 主監控循環 ---
                    self.update_status("初始化完成，開始監控")
                    while self.is_running:
                        try:
                            self._check_and_handle_timeout_warning()
                            if not self.is_running: break

                            current_time = time.time()
                            if current_time - self.last_cookie_update_time >= self.cookie_update_interval:
                                log_manager.info("準備執行定期 Cookie 更新...", device=self.device_id)

                                # 在更新 Cookie 前，點擊 "開始查詢"
                                log_manager.debug("嘗試獲取當前 frame 以點擊 '開始查詢'", device=self.device_id)
                                current_frame1 = self._get_frame1(timeout=15000) # 使用較短超時獲取
                                if current_frame1 and self.is_running:
                                    current_txn_frame = self._get_txn_frame(current_frame1, timeout=15000)
                                    if current_txn_frame and self.is_running:
                                        log_manager.info("嘗試在更新 Cookie 前點擊 '開始查詢'", device=self.device_id)
                                        if self._click_start_query_button(current_txn_frame):
                                            log_manager.info("成功在更新 Cookie 前點擊 '開始查詢'", device=self.device_id)
                                            # 點擊成功後，執行 Cookie 更新
                                            if not self._get_and_update_cookies():
                                                log_manager.warning("定期 Cookie 更新失敗 (在點擊查詢後)", device=self.device_id)
                                        else:
                                            log_manager.warning("在更新 Cookie 前點擊 '開始查詢' 失敗，仍嘗試更新 Cookie", device=self.device_id)
                                            # 即使點擊失敗，也嘗試更新 Cookie
                                            if not self._get_and_update_cookies():
                                                log_manager.warning("定期 Cookie 更新失敗 (在點擊查詢失敗後)", device=self.device_id)
                                    else:
                                        log_manager.warning("無法獲取當前 txnFrame 來點擊 '開始查詢'，跳過點擊，直接更新 Cookie", device=self.device_id)
                                        if not self._get_and_update_cookies():
                                            log_manager.warning("定期 Cookie 更新失敗 (無法獲取 txnFrame)", device=self.device_id)
                                else:
                                    log_manager.warning("無法獲取當前 frame1 來點擊 '開始查詢'，跳過點擊，直接更新 Cookie", device=self.device_id)
                                    if not self._get_and_update_cookies():
                                        log_manager.warning("定期 Cookie 更新失敗 (無法獲取 frame1)", device=self.device_id)

                                if not self.is_running: break

                            time.sleep(1) # 短暫休息
                        except Exception as monitor_loop_error:
                            if self.is_running:
                                self.log_with_screenshot('error', f"監控循環中發生錯誤: {monitor_loop_error}", "monitor_loop_error")
                                # 發生錯誤後，跳出監控子循環，回到主循環重試登入
                                break
                            else:
                                break # 如果已停止，則退出

                    # 如果是因為 is_running=False 跳出監控子循環，外層循環也會結束
                    if not self.is_running:
                        break

                except Exception as main_loop_error:
                    if self.is_running:
                        self.log_with_screenshot('error', f"主循環發生錯誤: {main_loop_error}", "main_loop_error")
                        self.update_status("發生錯誤，10秒後重試...")
                        # 等待一段時間再重試主循環
                        for _ in range(10):
                            if not self.is_running: break
                            time.sleep(1)
                    else:
                        break # 如果已停止，則退出主循環

        except Exception as start_error:
             if self.is_running: # 只有在運行中發生錯誤才記錄
                 self.log_with_screenshot('error', f"初始化或設置時發生錯誤: {start_error}", "start_setup_error")
                 self.is_running = False # 確保設置停止標誌

        # --- 清理資源 ---
        # 無論 start 如何退出 (正常停止或異常)，都會執行此處
        log_manager.debug("開始清理 Playwright 資源...", device=self.device_id)
        try:
            if self.page: self.page.close()
            if self.context: self.context.close()
            if self.browser: self.browser.close()
            if self.playwright: self.playwright.stop()
        except Exception as cleanup_error:
            log_manager.error(f"關閉瀏覽器資源時發生錯誤: {cleanup_error}", device=self.device_id)
        finally:
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            final_status = "已停止" if not self.is_running else "錯誤停止"
            self.update_status(final_status)
            log_manager.info(f"富邦銀行 ({self.bank_account}) 工作線程結束 ({final_status})", device=self.device_id)
            self.is_running = False # 再次確保

    def stop(self):
        """請求停止銀行操作 (由外部線程調用)"""
        if not self.is_running:
            return
        log_manager.info(f"請求停止富邦銀行 ({self.bank_account}) 操作...", device=self.device_id)
        self.is_running = False # 設置標誌，主循環會檢測到並退出
