# 銀行 OCR 驗證碼識別實現狀態

本文件記錄所有支援銀行的驗證碼 OCR 自動識別功能實現狀態。

## 📊 總覽統計

| 狀態 | 數量 | 百分比 |
|------|------|--------|
| ✅ 已完成 OCR | 10 | 83.3% |
| 🔄 待實現 OCR | 0 | 0.0% |
| ❌ 無需驗證碼 | 2 | 16.7% |
| **總計** | **12** | **100%** |

## 📋 詳細狀態列表

| 銀行代碼 | 銀行名稱 | OCR 狀態 | 統一接口 | 模組文件 | 備註 |
|----------|----------|----------|----------|----------|------|
| 012 | 富邦銀行 | ✅ 已完成 | ✅ 是 | `fubon_playwright.py` | 使用統一接口，選擇器：`#m1_captchaImage`, `#m1_userCaptcha` |
| 812 | 台新銀行 | ✅ 已完成 | ✅ 是 | `taixin_playwright.py` | 使用統一接口，選擇器：`#verify img`, `textbox[name="驗證碼"]` |
| 807 | 永豐銀行 | ✅ 已完成 | ✅ 是 | `sinopac_playwright.py` | 個人帳戶，選擇器：`img[alt="圖形驗證碼"]`, `input[placeholder="可按圖更換驗證碼"]` |
| 807 | 永豐銀行(企業) | 🔄 待實現 | ❌ 否 | `sinopac_corp_playwright.py` | 企業帳戶 |
| 826 | 樂天銀行 | ✅ 已完成 | ✅ 是 | `rakutenbank_playwright.py` | 選擇器：`generic:has(textbox[placeholder="驗證碼"]) img`, `textbox[placeholder="驗證碼"]` |
| 808 | 玉山銀行 | ❌ 無需驗證碼 | ❌ 否 | `yusan_playwright.py` | 登入頁面無驗證碼 |
| 013 | 國泰世華銀行 | ❌ 無需驗證碼 | ❌ 否 | `cathaybk_playwright.py` | 登入頁面無驗證碼 |
| 017 | 兆豐銀行 | ✅ 已完成 | ✅ 是 | `megabank_playwright.py` | 選擇器：通用驗證碼圖片, `textbox[placeholder*="請輸入驗證碼"]` |
| 007 | 第一銀行 | ✅ 已完成 | ✅ 是 | `firstbank_playwright.py` | 使用通用選擇器 |
| 824 | 連線銀行 | ✅ 已完成 | ✅ 是 | `linebank_playwright.py` | 使用通用選擇器 |
| 700 | 郵局 | ✅ 已完成 | ✅ 是 | `post_playwright.py` | 使用通用選擇器 |
| 823 | 將來銀行 | ✅ 已完成 | ✅ 是 | `nextbank_playwright.py` | 使用通用選擇器 |
| 008 | 華南銀行 | ✅ 已完成 | ✅ 是 | `hncb_playwright.py` | 使用通用選擇器 |
| - | 台灣銀行 | 🔄 待實現 | ❌ 否 | `twbank_playwright.py` | 未在 banks.py 中註冊 |

## 🎯 統一 OCR 接口說明

### 已實現統一接口的銀行

#### ✅ 富邦銀行 (012)
- **驗證碼圖片選擇器**: `#m1_captchaImage`
- **驗證碼輸入框選擇器**: `#m1_userCaptcha`
- **使用方式**: `self.handle_captcha_ocr(txn_frame)`
- **特殊說明**: 使用 iframe 結構，需要在 `txnFrame` 中操作

#### ✅ 台新銀行 (812)
- **驗證碼圖片選擇器**: `#verify img`
- **驗證碼輸入框選擇器**: `textbox[name="驗證碼"]` (role-based)
- **使用方式**: `self.handle_captcha_ocr(main_frame)`
- **特殊說明**: 使用手機版介面，在 `#main` iframe 中操作

#### ✅ 永豐銀行 (807)
- **驗證碼圖片選擇器**: `img[alt="圖形驗證碼"]`
- **驗證碼輸入框選擇器**: `input[placeholder="可按圖更換驗證碼"]`
- **使用方式**: `self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors)`
- **特殊說明**: 使用電腦版介面，直接在主頁面操作，支援自定義選擇器

#### ✅ 兆豐銀行 (017)
- **驗證碼圖片選擇器**: 通用驗證碼圖片選擇器
- **驗證碼輸入框選擇器**: `textbox[placeholder*="請輸入驗證碼"]`
- **使用方式**: `self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors)`
- **特殊說明**: 使用電腦版介面，驗證碼為5碼數字

#### ✅ 樂天銀行 (826)
- **驗證碼圖片選擇器**: `generic:has(textbox[placeholder="驗證碼"]) img` (精確定位驗證碼區域內的圖片)
- **驗證碼輸入框選擇器**: `textbox[placeholder="驗證碼"]` (role-based 選擇器)
- **使用方式**: `self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors)`
- **特殊說明**: 使用電腦版介面，需要代理設定，驗證碼為4位字母+數字組合，藍色字體白色背景

### 統一接口使用方法

#### 方法1：使用銀行專用選擇器（推薦）

```python
# 在銀行模組的自動填入方法中添加
try:
    # 定義銀行專用選擇器
    captcha_selectors = ['#m1_captchaImage']  # 富邦銀行專用
    input_selectors = ['#m1_userCaptcha']     # 富邦銀行專用

    if self.handle_captcha_ocr(frame, captcha_selectors, input_selectors):
        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
        self.update_status("已自動填入(含驗證碼)")
    else:
        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
        self.update_status("已自動填入(需手動驗證碼)")
except Exception as e:
    log_manager.warning(f"驗證碼處理時發生錯誤: {str(e)}", device=self.device_id)
```

#### 方法2：使用通用選擇器（備用）

```python
# 如果沒有提供自定義選擇器，會使用通用選擇器
if self.handle_captcha_ocr(frame):
    # 使用通用選擇器嘗試識別
    pass
```

## 📝 實現進度追蹤

### 下一步計畫

1. **永豐銀行** (807) - 個人帳戶
2. **永豐銀行企業版** (807) - 企業帳戶  
3. **樂天銀行** (826)
4. **玉山銀行** (808)
5. **國泰世華銀行** (013)
6. **兆豐銀行** (017)
7. **第一銀行** (007)
8. **連線銀行** (824)
9. **郵局** (700)
10. **將來銀行** (823)
11. **華南銀行** (008)
12. **台灣銀行** (未註冊)

### 實現檢查清單

對於每個待實現的銀行，需要檢查：

- [ ] 是否有驗證碼輸入需求
- [ ] 驗證碼圖片的選擇器
- [ ] 驗證碼輸入框的選擇器
- [ ] 頁面結構（iframe、frame 等）
- [ ] 是否需要自定義選擇器
- [ ] 測試 OCR 識別效果

## 🔧 技術說明

### OCR 功能架構

1. **OCR 管理器** (`ocr_manager.py`) - 核心 OCR 功能
2. **OCR 工具** (`ocr_utils.py`) - 高級 OCR 接口
3. **統一接口** (`base_playwright.py`) - 銀行模組統一接口
4. **自定義 OCR** (`custom_ocr/`) - 本地 OCR 模組

### 通用選擇器列表

統一接口提供了通用選擇器作為備用方案，當銀行模組沒有提供專用選擇器時使用：

**通用驗證碼圖片選擇器**:
- `img[src*="captcha"]`
- `img[src*="verify"]`
- `img[src*="code"]`
- `img[alt*="驗證"]`
- `img[title*="驗證"]`
- `img[id*="captcha"]`
- `img[id*="verify"]`
- `img[class*="captcha"]`
- `img[class*="verify"]`

**通用驗證碼輸入框選擇器**:
- `input[placeholder*="驗證"]`
- `input[name*="captcha"]`
- `input[name*="verify"]`
- `input[id*="captcha"]`
- `input[id*="verify"]`
- `input[class*="captcha"]`
- `input[class*="verify"]`

### 銀行專用選擇器

各銀行模組在自己的代碼中定義專用選擇器，確保最佳的識別效果：

**富邦銀行**:
- 圖片: `#m1_captchaImage`
- 輸入框: `#m1_userCaptcha`

**台新銀行**:
- 圖片: `#verify img`
- 輸入框: `textbox[name="驗證碼"]` (role-based)

**永豐銀行**:
- 圖片: `img[alt="圖形驗證碼"]`, `link[title="圖形驗證碼"] img`
- 輸入框: `input[placeholder="可按圖更換驗證碼"]`

**兆豐銀行**:
- 圖片: 通用驗證碼圖片選擇器
- 輸入框: `textbox[placeholder*="請輸入驗證碼"]`

**樂天銀行**:
- 圖片: `generic:has(textbox[placeholder="驗證碼"]) img` (精確定位驗證碼區域內的圖片)
- 輸入框: `textbox[placeholder="驗證碼"]` (role-based 選擇器)

## 🔧 故障排除

### 常見 OCR 失敗原因

#### 1. **選擇器問題**
- **症狀**: 日誌顯示「驗證碼自動識別失敗，需要手動輸入」
- **原因**: 選擇器無法正確定位驗證碼元素
- **解決方案**:
  - 檢查頁面結構，更新選擇器
  - 使用瀏覽器開發者工具確認元素屬性
  - 添加更多備用選擇器

#### 2. **Role-based 選擇器問題**
- **症狀**: `textbox[placeholder="驗證碼"]` 類型選擇器失效
- **原因**: 統一接口未正確處理 role-based 選擇器
- **解決方案**: 已修復，支援 `name=` 和 `placeholder=` 屬性

#### 3. **驗證碼圖片載入時機**
- **症狀**: 偶爾識別失敗，重試後成功
- **原因**: 驗證碼圖片動態載入，OCR 嘗試時圖片未完全載入
- **解決方案**:
  - 增加等待時間
  - 使用 `max_retries` 參數重試
  - 檢查圖片 `is_visible()` 狀態

#### 4. **驗證碼圖片特性**
- **症狀**: 選擇器正確但識別率低
- **原因**: 驗證碼圖片特性影響 OCR 識別
- **影響因素**:
  - 圖片大小（過小或過大）
  - 字體樣式（特殊字體、扭曲程度）
  - 背景干擾（噪點、線條、顏色）
  - 字符類型（純數字、字母+數字、大小寫混合）

### 調試方法

#### 1. **啟用調試日誌**
```python
# 在銀行模組中添加更詳細的日誌
log_manager.debug(f"嘗試的驗證碼圖片選擇器: {captcha_selectors}", device=self.device_id)
log_manager.debug(f"嘗試的驗證碼輸入框選擇器: {input_selectors}", device=self.device_id)
```

#### 2. **檢查元素定位**
- 使用瀏覽器開發者工具檢查驗證碼元素
- 確認選擇器是否正確匹配元素
- 檢查元素是否在 iframe 中

#### 3. **測試 OCR 功能**
- 手動截圖驗證碼圖片
- 使用 OCR 工具單獨測試圖片識別
- 調整 OCR 參數和預處理方法

---

**最後更新**: 2025-06-04
**更新者**: AI Assistant
**版本**: 1.1
