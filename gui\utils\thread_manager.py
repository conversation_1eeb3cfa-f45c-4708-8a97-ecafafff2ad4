import threading
import ctypes
from .logger import log_manager

class ThreadManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ThreadManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
        
    def _initialize(self):
        """初始化線程管理器"""
        self.active_threads = {}  # 設備ID -> (線程對象, 銀行實例)的映射
        self.thread_lock = threading.Lock()
        
    def _async_raise(self, tid, exctype):
        """強制終止線程"""
        if not isinstance(tid, ctypes.c_long):
            tid = ctypes.c_long(tid)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            return False
        elif res != 1:
            # 如果返回值大於1，則說明可能影響到其他線程，需要取消
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            return False
        return True
        
    def _terminate_thread(self, thread):
        """終止線程"""
        try:
            if thread.is_alive():
                thread_id = thread.ident
                if thread_id:
                    return self._async_raise(thread_id, SystemExit)
        except:
            pass
        return False
        
    def start_thread(self, device_id, target, args=()):
        """
        啟動新線程
        
        Args:
            device_id: 設備ID
            target: 線程執行的函數
            args: 傳遞給函數的參數元組
        
        Returns:
            bool: 是否成功啟動線程
        """
        with self.thread_lock:
            # 檢查是否已有運行中的線程
            if device_id in self.active_threads and self.active_threads[device_id][0].is_alive():
                log_manager.warning(f"設備 {device_id} 已有運行中的線程")
                return False
                
            # 創建新線程
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            
            # 保存線程
            self.active_threads[device_id] = (thread, None)
            
            # 啟動線程
            thread.start()
            log_manager.info(f"設備 {device_id} 的線程已啟動", device=f"設備 {device_id}")
            return True
            
    def stop_thread(self, device_id):
        """
        停止指定設備的線程
        
        Args:
            device_id: 設備ID
            
        Returns:
            bool: 是否成功停止線程
        """
        with self.thread_lock:
            if device_id not in self.active_threads:
                log_manager.warning(f"設備 {device_id} 沒有運行中的登入程序")
                return False
                
            thread, bank_instance = self.active_threads[device_id]
            
            if not thread.is_alive():
                log_manager.warning(f"設備 {device_id} 沒有運行中的登入程序")
                del self.active_threads[device_id]
                return False
                
            # 先停止銀行實例
            if bank_instance:
                bank_instance.stop()
                
            # 嘗試強制終止線程
            if self._terminate_thread(thread):
                log_manager.info(f"設備 {device_id} 的線程已停止", device=f"設備 {device_id}")
            else:
                log_manager.warning(f"無法強制終止設備 {device_id} 的線程")
                
            # 移除線程記錄
            del self.active_threads[device_id]
            return True
            
    def set_bank_instance(self, device_id, bank_instance):
        """設置設備對應的銀行實例"""
        with self.thread_lock:
            if device_id in self.active_threads:
                self.active_threads[device_id] = (self.active_threads[device_id][0], bank_instance)
            
    def is_thread_running(self, device_id):
        """
        檢查指定設備是否有運行中的線程
        
        Args:
            device_id: 設備ID
            
        Returns:
            bool: 是否有運行中的線程
        """
        with self.thread_lock:
            return (device_id in self.active_threads and 
                   self.active_threads[device_id][0].is_alive())
                   
    def stop_all_threads(self):
        """停止所有運行中的線程"""
        with self.thread_lock:
            for device_id in list(self.active_threads.keys()):
                self.stop_thread(device_id)

# 創建全局線程管理器實例
thread_manager = ThreadManager()
