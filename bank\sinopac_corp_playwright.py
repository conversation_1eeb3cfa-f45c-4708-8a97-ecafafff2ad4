"""
永豐銀行公司戶操作模組 (Playwright 版本)
"""

import time
import random
from playwright.sync_api import expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class SinopacCorpBank(BankBase):
    """永豐銀行公司戶操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        # 呼叫父類別的初始化方法，設定為電腦版模式
        super().__init__(
            bank_code="807",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=False,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = "https://b2b.sinopac.com/B2B/index.xhtml?glocale=zh_TW"
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = random.randint(30, 60)  # cookies 更新間隔（秒）
        self.keep_alive_interval = 300  # 保持登入間隔（秒）
        self.viewstate = None  # 存儲 ViewState
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 等待歡迎訊息出現
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first
            welcome_heading = main_frame.get_by_role("heading", name="歡迎使用 永豐銀行寰宇金融網")
            
            if welcome_heading.is_visible():
                log_manager.debug("登入成功", device=self.device_id)
                return True
                
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
            return False
            
    def navigate_to_account_page(self):
        """導航到帳戶查詢頁面"""
        try:
            # 點擊帳戶查詢
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            account_query_link = index_frame.get_by_role("link", name="帳戶查詢")
            account_query_link.click()
            
            # 點擊存款查詢
            deposit_query_link = index_frame.get_by_role("link", name="存款查詢")
            deposit_query_link.click()
            
            # 點擊交易明細查詢
            transaction_query_link = index_frame.get_by_role("link", name="交易明細查詢")
            transaction_query_link.click()
            
            # 等待帳號欄位出現
            main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first
            main_frame.get_by_role("cell", name="＊帳號/幣別").click()
            
            log_manager.debug("已進入交易明細查詢頁面", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"導航到帳戶查詢頁面時發生錯誤: {str(e)}",
                    "navigation_error")
            return False
            
    def verify_account(self):
        """驗證帳號"""
        try:
            # --- 帳號驗證重試邏輯 ---
            max_retries = 3
            retry_delay = 5
            account_verified = False
            main_frame = None # 初始化 main_frame

            for attempt in range(max_retries):
                if not self.is_running: return False # 每次重試前檢查

                log_manager.debug(f"開始驗證永豐公司戶帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                try:
                    # 獲取 iframe
                    index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
                    main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first

                    if not main_frame:
                         log_manager.warning(f"第 {attempt + 1} 次嘗試找不到 mainFrame", device=self.device_id)
                         # Frame 找不到，進入重試
                    else:
                        # 獲取下拉選單的 HTML
                        select_element = main_frame.locator("select#form\\:accountCombo_input")
                        # 等待元素附加到 DOM
                        select_element.wait_for(state="attached", timeout=10000)
                        select_html = select_element.evaluate("el => el.outerHTML")

                        # 檢查帳號是否存在於選單中
                        if str(self.bank_account) in select_html:
                            log_manager.info(f"帳號驗證成功: {self.bank_account} (嘗試 {attempt + 1})", device=self.device_id)
                            account_verified = True
                            break # 驗證成功，跳出重試循環
                        else:
                            log_manager.warning(f"第 {attempt + 1} 次嘗試未在下拉選單 HTML 中找到帳號: {self.bank_account}", device=self.device_id)
                            # 未找到，進入重試邏輯

                    # --- 重試處理 ---
                    if not account_verified and attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    elif not account_verified and attempt == max_retries - 1:
                        # 最後一次嘗試失敗
                        self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍未找到帳號 {self.bank_account}", "account_verify_retries_failed")
                        self.stop() # 驗證失敗，停止
                        return False

                except Exception as inner_e:
                    log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(inner_e)}", device=self.device_id)
                    if attempt < max_retries - 1:
                        log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    else:
                        self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(inner_e)}", "account_verify_error_final")
                        self.stop()
                        return False

            # --- 循環結束後 ---
            if account_verified:
                 # 確保 main_frame 有效才獲取 ViewState
                 if main_frame:
                     try:
                         # 獲取 ViewState
                         viewstate_input = main_frame.locator("input[name=\"javax.faces.ViewState\"]")
                         self.viewstate = viewstate_input.get_attribute("value")
                         log_manager.debug(f"已驗證帳號並獲取 ViewState: {self.viewstate}", device=self.device_id)
                         return True
                     except Exception as vs_error:
                          self.log_with_screenshot('error', f"驗證帳號成功但獲取 ViewState 失敗: {vs_error}", "viewstate_fetch_error")
                          self.stop()
                          return False
                 else:
                      self.log_with_screenshot('error', "帳號驗證成功但 main_frame 無效，無法獲取 ViewState", "main_frame_invalid_post_verify")
                      self.stop()
                      return False
            else:
                # 理論上如果失敗會在循環內 return False
                log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
                if self.is_running: self.stop() # 確保停止
                return False

        except Exception as e: # 捕捉 verify_account 方法本身的錯誤
            if self.is_running:
                self.log_with_screenshot('error',
                    f"驗證帳號方法執行時發生頂層錯誤: {str(e)}",
                    "account_verify_method_error")
                self.stop()
            return False
            
    def select_account(self):
        """選擇帳號"""
        try:
            # 獲取 iframe
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first
            
            # 點擊下拉選單
            account_label = main_frame.locator("label#form\\:accountCombo_label")
            account_label.click()
            
            # 選擇指定帳號
            account_panel = main_frame.locator("#form\\:accountCombo_panel")
            account_item = account_panel.get_by_text(f"{self.bank_account} TWD")
            account_item.click()
            
            log_manager.debug(f"已選擇帳號: {self.bank_account}", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"選擇帳號時發生錯誤: {str(e)}",
                    "select_account_error")
            return False
            
    def query_transactions(self):
        """查詢交易明細"""
        try:
            # 獲取 iframe
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first
            
            # 設置查詢開始日期為當天
            from datetime import datetime, timedelta
            today = datetime.now().strftime("%Y%m%d")  # 格式為 YYYYMMDD
            
            # 計算明天的日期
            tomorrow = (datetime.now() + timedelta(days=1)).strftime("%Y%m%d")  # 格式為 YYYYMMDD
            
            # 找到開始日期輸入框並設置值
            start_date_input = main_frame.locator('//*[@id="form:queryDateStart_input"]')
            start_date_input.click()
            start_date_input.fill(today)
            log_manager.debug(f"已設置查詢開始日期為: {today}", device=self.device_id)
            
            # 找到結束日期輸入框並設置值為明天
            end_date_input = main_frame.locator('//*[@id="form:queryDateEnd_input"]')
            end_date_input.click()
            end_date_input.fill(tomorrow)
            log_manager.debug(f"已設置查詢結束日期為: {tomorrow}", device=self.device_id)
            
            # 點擊查詢按鈕
            query_button = main_frame.get_by_role("button", name="查詢")
            query_button.click()
            
            log_manager.debug("已點擊查詢按鈕", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"查詢交易明細時發生錯誤: {str(e)}",
                    "query_transactions_error")
            return False
            
    def update_viewstate(self):
        """更新 ViewState 值"""
        try:
            # 獲取 iframe
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first
            
            # 獲取新的 ViewState
            viewstate_input = main_frame.locator("input[name=\"javax.faces.ViewState\"]")
            self.viewstate = viewstate_input.get_attribute("value")
            
            log_manager.debug(f"已更新 ViewState: {self.viewstate}", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"更新 ViewState 時發生錯誤: {str(e)}",
                    "update_viewstate_error")
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"驗證帳號時發生錯誤: {str(e)}",
                    "account_verify_error")
                self.stop()
            return False
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies，同時處理交易查詢"""
        if not self.is_running:
            return
            
        try:
            current_time = time.time()
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update < self.cookie_update_interval:
                return
                
            # 檢查是否有支付中訂單
            pending_orders = self.get_pending_orders(self.device_id)
            has_pending_orders = pending_orders and len(pending_orders) > 0
            
            # 更新 ViewState
            update_success = self.update_viewstate()
            if not update_success:
                self.log_with_screenshot('error',
                    "更新 ViewState 失敗，等待下次重試",
                    "viewstate_update_retry")
                return
                
            # 只有在有支付中訂單時才執行查詢操作
            if has_pending_orders:
                # 選擇帳號
                select_success = self.select_account()
                if not select_success:
                    self.log_with_screenshot('error',
                        "選擇帳號失敗，等待下次重試",
                        "select_account_retry")
                    return
                    
                # 重新查詢
                query_success = self.query_transactions()
                if not query_success:
                    self.log_with_screenshot('error',
                        "查詢交易明細失敗，等待下次重試",
                        "query_retry")
                    return
                    
            # 獲取所需的 cookies
            cookies = self.context.cookies()
            cookies_dict = {}
            
            # 提取需要的 cookies
            for cookie in cookies:
                if cookie["name"] in ["ciblocale", "B2BJSESSIONID", "sinopac_cookie"]:
                    cookies_dict[cookie["name"]] = cookie["value"]
                    
            # 加入 ViewState
            if self.viewstate:
                cookies_dict["javax"] = self.viewstate
                
            # 更新到 API
            if self.update_cookies(cookies_dict):
                self.cookies_updated = True
                self.last_cookie_update = current_time
                self.update_status("已更新 cookies")
                # 設定下次更新的隨機間隔(30-60秒)
                self.cookie_update_interval = random.randint(30, 60)
            else:
                self.log_with_screenshot('warning',
                    "更新 cookies 失敗",
                    "update_cookies_failed")
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"獲取 cookies 時發生錯誤: {str(e)}",
                    "get_cookies_error")
                    
            
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.should_auto_fill or not self.device_data:
            return False
            
        try:
            # 等待 iframe 加載
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            
            # 等待並填入網銀用戶代號
            bank_id_input = index_frame.get_by_placeholder("請輸入 網銀用戶代號")
            bank_id_input.fill(self.device_data['bank_id_number'])
            
            # 等待並填入使用者代號
            user_id_input = index_frame.get_by_placeholder("請輸入 使用者代號")
            user_id_input.fill(self.device_data['bank_user_id'])
            
            # 等待並填入使用者密碼
            password_input = index_frame.get_by_placeholder("請輸入 登入密碼")
            password_input.fill(self.device_data['bank_user_password'])
            
            # 等待頁面完全加載
            time.sleep(2)
            
            # 提示用戶需要手動輸入驗證碼
            log_manager.info("請手動輸入驗證碼並點擊登入按鈕", device=self.device_id)
            
            # 等待用戶手動操作
            self.update_status("請手動輸入驗證碼並點擊登入按鈕...")
            
            # 檢查是否出現特定按鈕，如果出現就提示用戶點擊
            try:
                special_button = index_frame.get_by_role("link", name="是，繼續本次登入")
                
                if special_button and special_button.is_visible():
                    log_manager.info("請點擊「是，繼續本次登入」按鈕", device=self.device_id)
                    self.update_status("請點擊「是，繼續本次登入」按鈕...")
            except Exception as e:
                log_manager.debug(f"未檢測到特定按鈕: {str(e)}", device=self.device_id)
            
            log_manager.debug("已自動填入登入表單", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入表單時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def keep_alive(self):
        """保持登入狀態"""
        try:
            # 點擊訊息區域以保持活動狀態
            index_frame = self.page.frame_locator("iframe[name=\"indexFrame\"]").first
            main_frame = index_frame.frame_locator("iframe[name=\"mainFrame\"]").first
            messages_area = main_frame.locator("[id=\"form\\:messages\"]")
            messages_area.click()
            
            log_manager.debug("已執行保持登入操作", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"保持登入操作時發生錯誤: {str(e)}",
                    "keep_alive_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟永豐銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                self.page.goto(self.login_url)
                self.page.wait_for_load_state("networkidle")
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                self.update_status("登入成功，導航到帳戶查詢頁面...")
                
                # 導航到帳戶查詢頁面
                if not self.navigate_to_account_page():
                    break
                    
                # 驗證帳號
                if not self.verify_account():
                    break
                    
                self.update_status("帳號驗證成功，開始監控...")
                
                # 選擇帳號
                if not self.select_account():
                    break
                    
                # 執行初次查詢並更新 cookies
                if not self.query_transactions():
                    break
                    
                # 立即更新一次 cookies
                self.get_and_update_cookies()
                # 主要監控循環
                last_keep_alive = time.time()
                last_update = time.time()
                
                while self.is_running:
                    try:
                        current_time = time.time()
                        
                        # 執行查詢相關操作和更新 cookies
                        if current_time - last_update >= self.cookie_update_interval:
                            self.get_and_update_cookies()
                            last_update = current_time
                            
                        # 執行保持登入操作
                        if current_time - last_keep_alive >= self.keep_alive_interval:
                            if not self.keep_alive():
                                break
                            last_keep_alive = current_time
                            
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"監控過程中發生錯誤: {str(e)}",
                                "monitor_error")
                            time.sleep(5)
                            
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
            
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止永豐銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
