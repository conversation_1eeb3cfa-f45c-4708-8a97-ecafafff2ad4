"""
OCR 管理器模組
負責自定義 OCR 模組的管理和 OCR 識別功能
"""

from gui.utils.logger import log_manager

class OCRManager:
    """OCR 管理器 - 使用自定義 OCR 模組"""

    def __init__(self):
        self.ocr_instance = None
        self.is_available = False
        # 初始化時檢查 OCR 功能是否可用
        self.check_installation()
        
    def check_installation(self):
        """檢查 OCR 功能是否可用"""
        try:
            # 檢查自定義 OCR 模組是否可用
            try:
                from custom_ocr import DdddOcr
                log_manager.info("自定義 OCR 模組已載入")
            except ImportError as e:
                log_manager.error(f"自定義 OCR 模組載入失敗: {e}")
                self.is_available = False
                return False

            # 嘗試創建 OCR 實例來驗證功能
            try:
                # 使用自定義 OCR 模組
                test_ocr = DdddOcr(show_ad=False)
                log_manager.info("OCR 功能已就緒，使用自定義模組")

                self.is_available = True
                return True
            except Exception as e:
                log_manager.error(f"創建 OCR 實例失敗: {e}")
                self.is_available = False
                return False

        except Exception as e:
            log_manager.error(f"檢查 OCR 安裝狀態時發生錯誤: {e}")
            self.is_available = False
            return False
    
    def get_ocr_instance(self):
        """獲取 OCR 實例"""
        if not self.is_available:
            return None

        if self.ocr_instance is None:
            try:
                from custom_ocr import DdddOcr
                # 使用自定義 OCR 模組
                log_manager.info("使用自定義模組創建 OCR 實例")
                self.ocr_instance = DdddOcr(show_ad=False)
                log_manager.info("OCR 實例創建成功（使用自定義模組）")
            except Exception as e:
                log_manager.error(f"創建 OCR 實例失敗: {e}")
                return None

        return self.ocr_instance
    
    def recognize_captcha(self, image_data, device_id=None):
        """
        識別驗證碼
        
        Args:
            image_data: 圖片數據（bytes）
            device_id: 設備 ID
            
        Returns:
            str: 識別結果，失敗時返回 None
        """
        ocr = self.get_ocr_instance()
        if not ocr:
            log_manager.warning("OCR 實例不可用", device=device_id)
            return None
        
        try:
            result = ocr.classification(image_data)
            if result:
                result = result.strip()
                log_manager.info(f"驗證碼識別成功: {result}", device=device_id)
                return result
            else:
                log_manager.warning("驗證碼識別結果為空", device=device_id)
                return None
        except Exception as e:
            log_manager.error(f"驗證碼識別失敗: {e}", device=device_id)
            return None

# 全局 OCR 管理器實例
ocr_manager = OCRManager()
