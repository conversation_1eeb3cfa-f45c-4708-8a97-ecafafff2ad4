# 系統依賴自動檢查與安裝

## 概述

銀行登入管理系統現在包含自動系統依賴檢查與安裝功能，確保客戶端在首次啟動時能夠自動配置所需的外部依賴。

## 功能特色

### 自動檢查與安裝的依賴項目

1. **Chocolatey** - Windows 包管理器
   - 自動下載並安裝 Chocolatey
   - 用於後續安裝其他依賴項目

2. **Node.js v22.x.x** - JavaScript 運行環境
   - 透過 Chocolatey 自動安裝 Node.js LTS 版本 22
   - 確保版本相容性

3. **npm** - Node.js 包管理器
   - 隨 Node.js 一起安裝
   - 用於安裝 Playwright

4. **Playwright** - 瀏覽器自動化工具
   - 透過 npm 自動安裝
   - 使用 CI 模式避免互動式安裝

5. **mitmproxy** - HTTP/HTTPS 代理工具
   - 自動下載 mitmproxy 12.1.1 版本
   - 解壓縮到 `dependencies/mitmproxy/` 目錄

### 目錄結構

```
bank_login/
├── dependencies/
│   └── mitmproxy/
│       ├── mitmdump.exe
│       ├── mitmproxy.exe
│       └── mitmweb.exe
├── system_check.py          # 系統依賴檢查模組
├── test_system_check.py     # 測試腳本
└── main.py                  # 主程式（已整合系統檢查）
```

## 安裝流程

### 自動安裝順序

1. **Chocolatey 安裝**
   ```powershell
   irm https://community.chocolatey.org/install.ps1|iex
   ```

2. **Node.js 安裝**
   ```powershell
   choco install nodejs-lts --version="22" -y
   ```

3. **Playwright 安裝**
   ```bash
   npm init playwright@latest -- --yes
   ```

4. **mitmproxy 下載**
   - 從 `https://downloads.mitmproxy.org/12.1.1/mitmproxy-12.1.1-windows-x86_64.zip` 下載
   - 解壓縮到 `dependencies/mitmproxy/`

### 環境變數處理

系統會自動重新載入環境變數，確保新安裝的工具能夠被正確識別。

## 使用方式

### 自動執行（推薦）

系統依賴檢查會在主程式啟動時自動執行：

```python
# main.py 中已整合
from system_check import system_checker

# 在應用啟動時自動執行
system_checker.run_full_check()
```

### 手動測試

可以使用測試腳本來單獨測試系統檢查功能：

```bash
python test_system_check.py
```

### 單獨使用

```python
from system_check import system_checker

# 檢查特定依賴
if not system_checker.check_nodejs():
    system_checker.install_nodejs()

# 執行完整檢查
success = system_checker.run_full_check()
```

## 錯誤處理

### 常見問題

1. **權限不足**
   - 安裝 Chocolatey 和 Node.js 需要管理員權限
   - 系統會自動要求提升權限

2. **網路連線問題**
   - 下載 mitmproxy 需要網路連線
   - 系統會記錄錯誤並繼續執行

3. **防毒軟體干擾**
   - 某些防毒軟體可能阻止下載或執行
   - 建議將應用程式加入白名單

### 日誌記錄

所有安裝過程都會記錄到應用程式日誌中：

```
logs/app_YYYY-MM-DD_HHMMSS.log
```

## 打包配置

### PyInstaller 配置

`build.py` 已更新以包含新的依賴檔案：

```python
'--add-data', 'dependencies;dependencies',  # 添加依賴檔案目錄
```

### 檔案包含

打包後的執行檔會包含：
- `system_check.py` - 系統檢查模組
- `dependencies/mitmproxy/` - mitmproxy 執行檔
- 相關的依賴檢查邏輯

## 開發注意事項

### 新增依賴檢查

要新增新的依賴檢查，請在 `SystemDependencyChecker` 類別中新增對應的方法：

```python
def check_new_dependency(self):
    """檢查新依賴是否已安裝"""
    # 實作檢查邏輯
    pass

def install_new_dependency(self):
    """安裝新依賴"""
    # 實作安裝邏輯
    pass
```

然後在 `run_full_check()` 方法中加入新的檢查。

### 測試

在修改系統檢查功能後，請執行測試腳本確保功能正常：

```bash
python test_system_check.py
```

## 版本相容性

- **Python**: >= 3.11
- **Node.js**: v22.x.x (LTS)
- **mitmproxy**: 12.1.1
- **Playwright**: 最新版本

## 安全考量

1. **下載驗證**: 建議加入檔案完整性檢查
2. **權限控制**: 僅在必要時要求管理員權限
3. **網路安全**: 使用 HTTPS 下載所有依賴
4. **錯誤處理**: 優雅處理安裝失敗的情況
