"""
測試系統依賴檢查功能
"""

import sys
import os

# 添加當前目錄到Python路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from system_check import system_checker

def main():
    """測試系統檢查功能"""
    print("開始測試系統依賴檢查...")
    
    # 測試各個檢查功能
    print("\n=== 檢查 Chocolatey ===")
    choco_exists = system_checker.check_chocolatey()
    print(f"Chocolatey 狀態: {'已安裝' if choco_exists else '未安裝'}")
    
    print("\n=== 檢查 Node.js ===")
    node_exists = system_checker.check_nodejs()
    print(f"Node.js 狀態: {'已安裝' if node_exists else '未安裝'}")
    
    print("\n=== 檢查 npm ===")
    npm_exists = system_checker.check_npm()
    print(f"npm 狀態: {'已安裝' if npm_exists else '未安裝'}")
    
    print("\n=== 檢查 Playwright ===")
    playwright_exists = system_checker.check_playwright()
    print(f"Playwright 狀態: {'已安裝' if playwright_exists else '未安裝'}")
    
    print("\n=== 檢查 mitmproxy 檔案 ===")
    mitmproxy_ready = system_checker.setup_mitmproxy()
    print(f"mitmproxy 狀態: {'就緒' if mitmproxy_ready else '未就緒'}")
    
    print("\n=== 完整檢查 ===")
    success = system_checker.run_full_check()
    print(f"完整檢查結果: {'成功' if success else '失敗'}")

if __name__ == "__main__":
    main()
