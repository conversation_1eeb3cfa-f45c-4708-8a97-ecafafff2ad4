"""
系統依賴檢查與自動安裝模組
檢查並自動安裝 Chocolatey、Node.js、npm、playwright 等依賴
"""

import os
import sys
import subprocess
import shutil
import time
import tempfile
import zipfile
import requests
from pathlib import Path
from gui.utils.logger import log_manager


class SystemDependencyChecker:
    """系統依賴檢查器"""
    
    def __init__(self):
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.dependencies_dir = os.path.join(self.current_dir, 'dependencies')
        self.mitmproxy_dir = os.path.join(self.dependencies_dir, 'mitmproxy')
        
    def check_command_exists(self, command):
        """檢查命令是否存在"""
        try:
            result = subprocess.run([command, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0, result.stdout.strip()
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False, ""
    
    def check_chocolatey(self):
        """檢查 Chocolatey 是否已安裝"""
        log_manager.info("檢查 Chocolatey 安裝狀態...")
        exists, version = self.check_command_exists('choco')
        if exists:
            log_manager.info(f"Chocolatey 已安裝: {version}")
            return True
        else:
            log_manager.warning("Chocolatey 未安裝")
            return False
    
    def install_chocolatey(self):
        """安裝 Chocolatey"""
        log_manager.info("正在安裝 Chocolatey...")
        try:
            # 使用 PowerShell 安裝 Chocolatey
            install_script = 'irm https://community.chocolatey.org/install.ps1|iex'
            result = subprocess.run([
                'powershell', '-ExecutionPolicy', 'Bypass', '-Command', install_script
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                log_manager.info("Chocolatey 安裝成功")
                # 重新載入環境變數
                self._refresh_environment()
                return True
            else:
                log_manager.error(f"Chocolatey 安裝失敗: {result.stderr}")
                return False
                
        except Exception as e:
            log_manager.error(f"安裝 Chocolatey 時發生錯誤: {str(e)}")
            return False
    
    def check_nodejs(self):
        """檢查 Node.js 是否已安裝且版本正確"""
        log_manager.info("檢查 Node.js 安裝狀態...")
        exists, version = self.check_command_exists('node')
        if exists:
            # 檢查版本是否為 v22.x.x
            if version.startswith('v22.'):
                log_manager.info(f"Node.js 已安裝且版本正確: {version}")
                return True
            else:
                log_manager.warning(f"Node.js 版本不正確: {version}，需要 v22.x.x")
                return False
        else:
            log_manager.warning("Node.js 未安裝")
            return False
    
    def install_nodejs(self):
        """安裝 Node.js"""
        log_manager.info("正在安裝 Node.js...")
        try:
            # 確保 Chocolatey 已安裝
            if not self.check_chocolatey():
                if not self.install_chocolatey():
                    return False
            
            # 使用 Chocolatey 安裝 Node.js
            result = subprocess.run([
                'choco', 'install', 'nodejs-lts', '--version=22', '-y'
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                log_manager.info("Node.js 安裝成功")
                # 重新載入環境變數
                self._refresh_environment()
                return True
            else:
                log_manager.error(f"Node.js 安裝失敗: {result.stderr}")
                return False
                
        except Exception as e:
            log_manager.error(f"安裝 Node.js 時發生錯誤: {str(e)}")
            return False
    
    def check_npm(self):
        """檢查 npm 是否已安裝"""
        log_manager.info("檢查 npm 安裝狀態...")
        exists, version = self.check_command_exists('npm')
        if exists:
            log_manager.info(f"npm 已安裝: {version}")
            return True
        else:
            log_manager.warning("npm 未安裝")
            return False
    
    def check_playwright(self):
        """檢查 playwright 是否已安裝"""
        log_manager.info("檢查 playwright 安裝狀態...")
        try:
            # 檢查 playwright 命令是否存在
            result = subprocess.run(['npx', 'playwright', '--version'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                log_manager.info(f"Playwright 已安裝: {result.stdout.strip()}")
                return True
            else:
                log_manager.warning("Playwright 未安裝")
                return False
        except Exception as e:
            log_manager.warning(f"檢查 Playwright 時發生錯誤: {str(e)}")
            return False
    
    def install_playwright(self):
        """安裝 Playwright"""
        log_manager.info("正在安裝 Playwright...")
        try:
            # 確保 Node.js 和 npm 已安裝
            if not self.check_nodejs():
                if not self.install_nodejs():
                    return False
            
            if not self.check_npm():
                log_manager.error("npm 未安裝，無法安裝 Playwright")
                return False
            
            # 建立臨時目錄進行安裝
            with tempfile.TemporaryDirectory() as temp_dir:
                os.chdir(temp_dir)
                
                # 初始化 playwright 專案（自動回答所有問題）
                env = os.environ.copy()
                env['CI'] = 'true'  # 設定 CI 環境變數以跳過互動式問題
                
                result = subprocess.run([
                    'npm', 'init', 'playwright@latest', '--', '--yes'
                ], capture_output=True, text=True, timeout=300, env=env)
                
                if result.returncode == 0:
                    log_manager.info("Playwright 安裝成功")
                    return True
                else:
                    log_manager.error(f"Playwright 安裝失敗: {result.stderr}")
                    return False
                    
        except Exception as e:
            log_manager.error(f"安裝 Playwright 時發生錯誤: {str(e)}")
            return False
        finally:
            # 回到原始目錄
            os.chdir(self.current_dir)
    
    def setup_mitmproxy(self):
        """設定 mitmproxy 檔案"""
        log_manager.info("檢查 mitmproxy 檔案...")
        
        # 檢查檔案是否存在
        required_files = ['mitmdump.exe', 'mitmproxy.exe', 'mitmweb.exe']
        missing_files = []
        
        for file in required_files:
            file_path = os.path.join(self.mitmproxy_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
        
        if missing_files:
            log_manager.warning(f"缺少 mitmproxy 檔案: {missing_files}")
            return self._download_mitmproxy()
        else:
            log_manager.info("mitmproxy 檔案已就緒")
            return True
    
    def _download_mitmproxy(self):
        """下載並解壓縮 mitmproxy"""
        log_manager.info("正在下載 mitmproxy...")
        try:
            url = "https://downloads.mitmproxy.org/12.1.1/mitmproxy-12.1.1-windows-x86_64.zip"
            
            # 下載檔案
            response = requests.get(url, stream=True, timeout=300)
            response.raise_for_status()
            
            # 儲存到臨時檔案
            with tempfile.NamedTemporaryFile(delete=False, suffix='.zip') as temp_file:
                for chunk in response.iter_content(chunk_size=8192):
                    temp_file.write(chunk)
                temp_zip_path = temp_file.name
            
            # 解壓縮
            with zipfile.ZipFile(temp_zip_path, 'r') as zip_ref:
                # 只解壓縮需要的檔案
                for file in ['mitmdump.exe', 'mitmproxy.exe', 'mitmweb.exe']:
                    try:
                        zip_ref.extract(file, self.mitmproxy_dir)
                        log_manager.info(f"已解壓縮: {file}")
                    except KeyError:
                        log_manager.warning(f"壓縮檔中找不到: {file}")
            
            # 清理臨時檔案
            os.unlink(temp_zip_path)
            
            log_manager.info("mitmproxy 下載並設定完成")
            return True
            
        except Exception as e:
            log_manager.error(f"下載 mitmproxy 時發生錯誤: {str(e)}")
            return False
    
    def _refresh_environment(self):
        """重新載入環境變數"""
        try:
            # 重新載入 PATH 環境變數
            result = subprocess.run([
                'powershell', '-Command', 
                '[Environment]::GetEnvironmentVariable("PATH", "Machine") + ";" + [Environment]::GetEnvironmentVariable("PATH", "User")'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                new_path = result.stdout.strip()
                os.environ['PATH'] = new_path
                log_manager.info("環境變數已重新載入")
        except Exception as e:
            log_manager.warning(f"重新載入環境變數時發生錯誤: {str(e)}")
    
    def run_full_check(self):
        """執行完整的系統檢查與安裝"""
        log_manager.info("開始系統依賴檢查...")
        
        success = True
        
        # 檢查並安裝 Chocolatey
        if not self.check_chocolatey():
            if not self.install_chocolatey():
                success = False
        
        # 檢查並安裝 Node.js
        if not self.check_nodejs():
            if not self.install_nodejs():
                success = False
        
        # 檢查 npm（通常隨 Node.js 一起安裝）
        if not self.check_npm():
            log_manager.error("npm 未安裝，請檢查 Node.js 安裝")
            success = False
        
        # 檢查並安裝 Playwright
        if not self.check_playwright():
            if not self.install_playwright():
                success = False
        
        # 設定 mitmproxy
        if not self.setup_mitmproxy():
            success = False
        
        if success:
            log_manager.info("所有系統依賴檢查完成")
        else:
            log_manager.error("部分系統依賴安裝失敗")
        
        return success


# 建立全域實例
system_checker = SystemDependencyChecker()
