import tkinter as tk
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import requests
import json
import os
import base64
from gui.utils.logger import log_manager
from gui.utils.config import config_manager

class LoginFrame(ttk.Frame):
    def __init__(self, parent):
        super().__init__(parent)
        log_manager.debug("初始化登入框架")
        self.settings_file = "device_settings.json"
        self._init_ui()
        self._load_saved_credentials()
        
    def _init_ui(self):
        try:
            # 設置樣式
            style = ttk.Style()
            style.configure("Login.Title.TLabel",
                font=("Noto Sans TC", 24, "bold")
            )
            style.configure("Login.TLabel",
                font=("Noto Sans TC", 14)
            )
            style.configure("Login.TEntry",
                font=("Noto Sans TC", 14)
            )
            style.configure("Login.Error.TLabel",
                font=("Noto Sans TC", 12),
                foreground="red"
            )
            style.configure("Login.TButton",
                font=("Noto Sans TC", 14)
            )
            
            # 創建登入框架
            login_frame = ttk.Frame(self)
            login_frame.pack(expand=True)
            
            # 標題
            title_label = ttk.Label(
                login_frame,
                text="銀行登入管理系統",
                style="Login.Title.TLabel",
                padding=(0, 0, 0, 40)
            )
            title_label.pack()
            
            # 表單框架
            form_frame = ttk.Frame(login_frame)
            form_frame.pack()
            
            # 帳號
            username_frame = ttk.Frame(form_frame)
            username_frame.pack(fill=X, pady=5)
            
            ttk.Label(
                username_frame,
                text="帳號",
                style="Login.TLabel",
                width=6
            ).pack(side=LEFT)
            
            self.username_var = tk.StringVar()
            self.username_entry = ttk.Entry(
                username_frame,
                textvariable=self.username_var,
                style="Login.TEntry",
                width=25
            )
            self.username_entry.pack(side=LEFT, padx=5)
            
            # 密碼
            password_frame = ttk.Frame(form_frame)
            password_frame.pack(fill=X, pady=5)
            
            ttk.Label(
                password_frame,
                text="密碼",
                style="Login.TLabel",
                width=6
            ).pack(side=LEFT)
            
            self.password_var = tk.StringVar()
            self.password_entry = ttk.Entry(
                password_frame,
                textvariable=self.password_var,
                style="Login.TEntry",
                width=25,
                show="*"
            )
            self.password_entry.pack(side=LEFT, padx=5)
            
            # 記住帳號密碼選項
            remember_frame = ttk.Frame(form_frame)
            remember_frame.pack(fill=X, pady=5)
            
            # 空白 Label 用於對齊
            ttk.Label(
                remember_frame,
                text="",
                style="Login.TLabel",
                width=6
            ).pack(side=LEFT)
            
            self.remember_var = tk.BooleanVar()
            self.remember_checkbox = ttk.Checkbutton(
                remember_frame,
                text="記住帳號密碼",
                variable=self.remember_var,
                style="Login.TCheckbutton"
            )
            self.remember_checkbox.pack(side=LEFT, padx=5)
            
            # 錯誤訊息
            self.error_label = ttk.Label(
                form_frame,
                text="",
                style="Login.Error.TLabel"
            )
            self.error_label.pack(pady=10)
            
            # 登入按鈕
            self.login_button = ttk.Button(
                form_frame,
                text="登入",
                command=self.login,
                style="Login.TButton",
                width=15
            )
            self.login_button.pack(pady=10)
            
            log_manager.debug("登入界面UI初始化完成")
            
            # 綁定回車鍵
            self.username_entry.bind('<Return>', lambda e: self.password_entry.focus_set())
            self.password_entry.bind('<Return>', lambda e: self.do_login())
            
            # 設置初始焦點
            self.username_entry.focus_set()
            
        except Exception as e:
            log_manager.exception("初始化登入界面時發生錯誤")
            raise
            
    def do_login(self, event=None):
        """執行登入操作"""
        try:
            username = self.username_var.get()
            password = self.password_var.get()
            
            if not username or not password:
                self.error_label.config(text="請輸入帳號和密碼")
                return
                
            log_manager.info(f"嘗試登入，用戶名：{username}")
            
            response = requests.post(
                config_manager.get_api_url("/api/twd/member/login"),
                json={
                    "username": username,
                    "password": password
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    token = data["data"]["token"]
                    log_manager.info("登入成功")
                    
                    # 如果勾選了記住帳號密碼，就儲存
                    if self.remember_var.get():
                        self._save_credentials()
                    else:
                        # 如果沒有勾選，則刪除已儲存的資訊
                        self._delete_saved_credentials()
                    
                    # 獲取主窗口
                    main_window = self.winfo_toplevel()
                    if hasattr(main_window, 'on_login_success'):
                        main_window.on_login_success(token)
                    else:
                        log_manager.error("主窗口缺少 on_login_success 方法")
                        self.error_label.config(text="系統錯誤")
                else:
                    error_message = data.get("message", "登入失敗")
                    log_manager.error(f"登入失敗：{error_message}")
                    self.error_label.config(text=error_message)
            else:
                log_manager.error(f"登入請求失敗：HTTP {response.status_code}")
                self.error_label.config(text="登入請求失敗")
                
        except requests.exceptions.RequestException as e:
            log_manager.error(f"登入時網絡錯誤：{str(e)}")
            self.error_label.config(text="連接服務器失敗")
        except Exception as e:
            log_manager.exception("登入過程中發生錯誤")
            self.error_label.config(text="登入過程中發生錯誤")
            
    def login(self, event=None):
        """登入按鈕點擊處理"""
        self.do_login()
        
    def _save_credentials(self):
        """儲存帳號密碼到 device_settings.json 的 global 區塊"""
        try:
            # 讀取設定檔
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # 更新 global 區塊
            settings['global'] = {
                "username": self.username_var.get(),
                "password": base64.b64encode(self.password_var.get().encode()).decode()
            }
            
            # 寫回檔案
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2)
            log_manager.debug("已儲存帳號密碼到 device_settings.json")
        except Exception as e:
            log_manager.error(f"儲存帳號密碼時發生錯誤: {str(e)}")
            
    def _load_saved_credentials(self):
        """從 device_settings.json 載入已儲存的帳號密碼"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    if 'global' in settings:
                        global_settings = settings['global']
                        self.username_var.set(global_settings.get("username", ""))
                        password = base64.b64decode(global_settings.get("password", "")).decode()
                        self.password_var.set(password)
                        self.remember_var.set(True)
                        log_manager.debug("已從 device_settings.json 載入儲存的帳號密碼")
        except Exception as e:
            log_manager.error(f"載入儲存的帳號密碼時發生錯誤: {str(e)}")
            
    def _delete_saved_credentials(self):
        """清空 device_settings.json 中的帳號密碼"""
        try:
            # 讀取設定檔
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # 清空 global 區塊的帳號密碼
            settings['global'] = {}
            
            # 寫回檔案
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2)
            log_manager.debug("已清空 device_settings.json 中的帳號密碼")
        except Exception as e:
            log_manager.error(f"清空帳號密碼時發生錯誤: {str(e)}")
