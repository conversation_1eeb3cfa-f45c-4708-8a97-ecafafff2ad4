# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虛擬環境
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# uv 包管理器
uv.lock
.python-version

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings

# 環境變數
.env
.env.*
!.env.example

# 日誌
*.log
logs/
log/

# 系統檔案
.DS_Store
Thumbs.db
desktop.ini

# 專案特定
*.sqlite3
.coverage
htmlcov/
.pytest_cache/
.mypy_cache/
docs/
images/
device_settings.json

# PyInstaller
*.spec
*.build/
*.onefile-build/
release/

# Chrome Driver
chromedriver.exe
chromedriver

playwright_cache/
locks/