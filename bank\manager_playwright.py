"""
銀行管理器模組 (Playwright 版本)
負責管理所有銀行實例的創建和操作
"""

import threading
import time
import gc
import psutil
import importlib
from gui.utils.logger import log_manager
from .banks import bank_registry

class BankManager:
    """銀行管理器 (Playwright 版本)"""
    
    def __init__(self, device_manager=None):
        self.banks = {}  # 存儲所有運行中的銀行實例，key 為 device_id
        self.threads = {}  # 存儲所有運行中的線程，key 為 device_id
        self.is_running = True
        self.memory_monitor_thread = None
        self._memory_check_interval = 5 * 60  # 5分鐘檢查一次記憶體
        self._memory_threshold = 1024 * 1024 * 1024  # 1GB 記憶體閾值
        self._cleanup_interval = 10 * 60  # 10分鐘強制清理一次
        self._last_cleanup_time = {}  # 記錄每個設備最後清理時間
        self.device_manager = device_manager  # 存儲 device_manager 實例
        self.start_memory_monitor()
        
    def perform_memory_cleanup(self, bank, device_id, force=False):
        """執行記憶體清理"""
        try:
            if not bank.page or not bank.browser:
                return

            # 獲取清理前的記憶體使用量
            process = psutil.Process()
            before_cleanup = process.memory_info().rss
            before_mb = before_cleanup / 1024 / 1024

            # 執行 Python GC
            gc.collect()
            
            # 獲取清理後的記憶體使用量
            after_cleanup = process.memory_info().rss
            after_mb = after_cleanup / 1024 / 1024
            freed_mb = before_mb - after_mb
            
            # 更新最後清理時間
            self._last_cleanup_time[device_id] = time.time()
            
            # 記錄清理結果
            if force:
                log_manager.info(f"執行強制記憶體清理 - 清理前: {before_mb:.2f}MB, 清理後: {after_mb:.2f}MB, 釋放: {freed_mb:.2f}MB", device=device_id)
            else:
                log_manager.debug(f"執行定期記憶體清理 - 清理前: {before_mb:.2f}MB, 清理後: {after_mb:.2f}MB, 釋放: {freed_mb:.2f}MB", device=device_id)
                
        except Exception as e:
            log_manager.error(f"執行記憶體清理時發生錯誤: {str(e)}", device=device_id)
        
    def start_memory_monitor(self):
        """啟動記憶體監控線程"""
        def monitor_routine():
            while self.is_running:
                try:
                    current_time = time.time()
                    
                    # 檢查每個運行中的銀行實例
                    for device_id, bank in list(self.banks.items()):
                        if not bank.is_running:
                            continue
                            
                        try:
                            # 獲取進程的記憶體使用量
                            process = psutil.Process()
                            memory_info = process.memory_info()
                            memory_mb = memory_info.rss / 1024 / 1024
                            
                            # 記錄記憶體使用情況
                            log_manager.debug(f"瀏覽器記憶體使用: {memory_mb:.2f}MB", device=device_id)
                            
                            # 檢查是否需要執行定期清理
                            last_cleanup = self._last_cleanup_time.get(device_id, 0)
                            if current_time - last_cleanup >= self._cleanup_interval:
                                self.perform_memory_cleanup(bank, device_id)
                            
                            # 如果記憶體使用超過閾值，執行強制清理
                            elif memory_info.rss > self._memory_threshold:
                                log_manager.info(f"記憶體使用量過高 ({memory_mb:.2f}MB)，執行強制清理", device=device_id)
                                self.perform_memory_cleanup(bank, device_id, force=True)
                                
                        except Exception as e:
                            log_manager.error(f"監控記憶體時發生錯誤: {str(e)}", device=device_id)
                            
                except Exception as e:
                    log_manager.error(f"記憶體監控線程發生錯誤: {str(e)}")
                    
                time.sleep(self._memory_check_interval)
                
        self.memory_monitor_thread = threading.Thread(
            target=monitor_routine,
            name="MemoryMonitorThread",
            daemon=True
        )
        self.memory_monitor_thread.start()
        log_manager.info("記憶體監控線程已啟動")
        
    def start_bank_login(self, bank_code, bank_account, device_id=None, status_callback=None, use_proxy: bool = False, device_data: dict = None): # 新增 use_proxy 和 device_data 參數
        """開始銀行登入流程"""
        device_id = device_id or bank_account
        
        # 如果該設備已經在運行，先停止它
        if device_id in self.banks:
            self.stop_bank_login(device_id)
            
        # 獲取銀行資訊
        bank_info = bank_registry.get_bank_by_code(bank_code)
        if not bank_info:
            raise ValueError(f"不支援的銀行代碼: {bank_code}")
            
        try:
            # 獲取設備資料 (如果未傳入)
            if not device_data and self.device_manager:
                device_data = self.device_manager.get_device_by_id(device_id)
                if not device_data:
                    log_manager.warning(f"找不到設備 {device_id} 的資料", device=device_id)
            elif not device_data:
                 log_manager.warning(f"未提供設備 {device_id} 的資料", device=device_id)
            
            # 獲取帳戶類型
            account_type = device_data.get("account_type", 1) if device_data else 1
            
            # 獲取模組資訊
            module_info = bank_registry.get_bank_module_info(bank_code, account_type)
            if not module_info:
                raise ValueError(f"不支援的帳戶類型: {account_type}")
            
            # 動態導入銀行模組
            module_name = f"{module_info.module}_playwright"
            module = importlib.import_module(f".{module_name}", package="bank")
            
            # 使用模組資訊中定義的類別名稱
            bank_class = getattr(module, module_info.class_name)
            
            # 創建銀行實例，傳入設備資料和代理設定
            # 注意：RakutenBank 的 __init__ 需要 use_proxy，其他 BankBase 子類也需要
            # 我們假設所有銀行類別的 __init__ 都接受 bank_account, device_id, device_data, use_proxy
            # 如果有銀行類別不接受 use_proxy，需要單獨處理
            try:
                self.banks[device_id] = bank_class(
                    bank_account=bank_account,
                    device_id=device_id,
                    device_data=device_data,
                    use_proxy=use_proxy # 傳遞代理設定
                )
            except TypeError as te:
                # 處理某些銀行類別可能不接受 use_proxy 的情況
                if 'use_proxy' in str(te):
                    log_manager.warning(f"銀行類別 {bank_class.__name__} 不支援 use_proxy 參數，將忽略代理設定。", device=device_id)
                    self.banks[device_id] = bank_class(
                        bank_account=bank_account,
                        device_id=device_id,
                        device_data=device_data
                    )
                else:
                    raise # 重新拋出其他 TypeError
            
            # 設置狀態回調
            if status_callback:
                self.banks[device_id].set_status_callback(status_callback)
                
            # 設置 bank_manager 引用
            self.banks[device_id].set_bank_manager(self)
                
            # 在新線程中啟動
            self.threads[device_id] = threading.Thread(
                target=self.banks[device_id].start,
                name=f"BankThread-{device_id}"
            )
            self.threads[device_id].daemon = True
            self.threads[device_id].start()
            
            # 使用 INFO 級別記錄重要的狀態變更
            log_manager.info(f"設備 {device_id} 的線程已啟動", device=device_id)
            
        except ImportError as e:
            log_manager.error(f"無法導入銀行模組 {module_name}: {str(e)}", device=device_id)
            raise ValueError(f"不支援的銀行代碼: {bank_code}")
        except AttributeError as e:
            log_manager.error(f"無法找到銀行類別 {bank_info.class_name}: {str(e)}", device=device_id)
            raise ValueError(f"不支援的銀行代碼: {bank_code}")
        except Exception as e:
            log_manager.error(f"創建銀行實例時發生錯誤: {str(e)}", device=device_id)
            raise
        
    def stop_bank_login(self, device_id):
        """停止指定設備的銀行操作"""
        if device_id in self.banks:
            bank = self.banks[device_id]
            
            try:
                bank.stop()
                log_manager.info(f"設備 {device_id} 的線程已停止", device=device_id)
                
            except Exception as e:
                log_manager.error(f"停止銀行操作時發生錯誤: {str(e)}", device=device_id)
            
            # 清理資源
            self.banks.pop(device_id, None)
            self.threads.pop(device_id, None)
            self._last_cleanup_time.pop(device_id, None)
            
    def stop_current_bank(self):
        """停止當前所有銀行操作"""
        self.is_running = False
        
        # 等待記憶體監控線程結束
        if self.memory_monitor_thread:
            self.memory_monitor_thread.join(timeout=5)
            
        # 停止所有銀行實例
        for device_id in list(self.banks.keys()):
            self.stop_bank_login(device_id)
            
    def __del__(self):
        """析構函數"""
        self.stop_current_bank()
