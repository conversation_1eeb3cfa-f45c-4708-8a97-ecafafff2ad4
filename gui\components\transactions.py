import tkinter as tk
from tkinter import ttk, font, filedialog
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import requests
from datetime import datetime, timedelta
import calendar
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import numbers
import os
from gui.utils.logger import log_manager
from gui.utils.config import config_manager
from gui.utils.async_utils import async_utils

class TransactionsFrame(ttk.Frame):
    def __init__(self, parent, app_state):
        super().__init__(parent)
        self.parent = parent
        self.app_state = app_state
        self.loading = False  # 加載狀態標記
        self.devices = []  # 存儲設備列表
        self.current_transactions = []  # 存儲當前查詢結果
        self.current_total_row = {}  # 存儲當前統計資料
        log_manager.debug("初始化交易記錄框架")
        self._init_ui()
        
    def _init_ui(self):
        try:
            # 設置樣式
            style = ttk.Style()
            style.configure("Treeview.Heading",
                font=("Noto Sans TC", 11, "bold"),
                background="#2C3E50",
                foreground="white",
                relief="raised",
                borderwidth=1
            )
            style.map("Treeview.Heading",
                background=[("active", "#34495E")],
                foreground=[("active", "white")]
            )
            style.configure("Treeview",
                font=("Noto Sans TC", 10)
            )
            style.configure("Search.TLabel",
                font=("Noto Sans TC", 10)
            )
            style.configure("Search.TCombobox",
                font=("Noto Sans TC", 10)
            )
            style.configure("Search.TRadiobutton",
                font=("Noto Sans TC", 10)
            )
            style.configure("Search.TButton",
                font=("Noto Sans TC", 10)
            )
            
            # 搜索條件框架
            search_frame = ttk.LabelFrame(self, text="搜索條件", padding=10)
            search_frame.pack(fill=X, padx=10, pady=5)
            
            # 設備選擇
            device_frame = ttk.Frame(search_frame)
            device_frame.pack(fill=X, pady=5)
            
            ttk.Label(device_frame, text="設備:", style="Search.TLabel").pack(side=LEFT)
            self.device_var = tk.StringVar(value="全部")
            self.device_combo = ttk.Combobox(device_frame, textvariable=self.device_var, style="Search.TCombobox")
            self.device_combo.pack(side=LEFT, padx=(10, 0))
            
            # 時間範圍選擇和搜索按鈕框架
            time_search_frame = ttk.Frame(search_frame)
            time_search_frame.pack(fill=X, pady=5)
            
            # 時間範圍選擇
            ttk.Label(time_search_frame, text="時間範圍:", style="Search.TLabel").pack(side=LEFT)
            self.time_var = tk.StringVar(value="today")
            time_options = [
                ("今日", "today"),
                ("昨日", "yesterday"),
                ("本周", "this_week"),
                ("上周", "last_week"),
                ("本月", "this_month"),
                ("上月", "last_month")
            ]
            
            for text, value in time_options:
                ttk.Radiobutton(
                    time_search_frame,
                    text=text,
                    value=value,
                    variable=self.time_var,
                    style="Search.TRadiobutton"
                ).pack(side=LEFT, padx=5)
            
            # 按鈕框架
            button_frame = ttk.Frame(time_search_frame)
            button_frame.pack(side=LEFT, padx=(20, 0))
            
            # 搜索按鈕
            self.search_button = ttk.Button(
                button_frame,
                text="搜索",
                command=self.search_transactions,
                style="Search.TButton"
            )
            self.search_button.pack(side=LEFT)
            
            # 匯出按鈕
            self.export_button = ttk.Button(
                button_frame,
                text="匯出",
                command=self.export_transactions,
                style="Search.TButton",
                state="disabled"  # 初始時禁用
            )
            self.export_button.pack(side=LEFT, padx=(10, 0))
            
            # 加載指示器
            self.loading_label = ttk.Label(
                button_frame,
                text="正在加載...",
                foreground="gray",
                style="Search.TLabel"
            )
            
            # 交易列表框架
            list_frame = ttk.Frame(self)
            list_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
            
            # 交易列表
            self.tree = ttk.Treeview(
                list_frame,
                columns=("date", "device", "money", "summary", "sof", "sof_db", "avmoney"),
                show="headings",
                height=15
            )
            
            # 定義列
            headers = [
                ("date", "日期", 150),
                ("device", "設備", 150),
                ("money", "金額", 100),
                ("summary", "摘要", 150),
                ("sof", "手續費", 100),
                ("sof_db", "單筆手續費", 100),
                ("avmoney", "結算金額", 100)
            ]
            
            # 設置列
            for col, text, width in headers:
                self.tree.heading(col, text=text, anchor=W)
                self.tree.column(col, width=width, anchor=W)
            
            # 添加滾動條
            scrollbar = ttk.Scrollbar(list_frame, orient=VERTICAL, command=self.tree.yview)
            self.tree.configure(yscrollcommand=scrollbar.set)
            
            # 佈局交易列表和滾動條
            self.tree.pack(side=LEFT, fill=BOTH, expand=True)
            scrollbar.pack(side=RIGHT, fill=Y)
            
            # 統計信息框架
            stats_frame = ttk.LabelFrame(self, text="統計信息", padding=10)
            stats_frame.pack(fill=X, padx=10, pady=5)
            
            # API 統計資訊
            total_stats = ttk.Frame(stats_frame)
            total_stats.pack(fill=X, expand=True)
            
            self.total_money = ttk.Label(total_stats, text="總金額: $0", style="Search.TLabel")
            self.total_money.pack(side=LEFT, padx=20)
            self.total_sof = ttk.Label(total_stats, text="手續費: $0", style="Search.TLabel")
            self.total_sof.pack(side=LEFT, padx=20)
            self.total_sof_db = ttk.Label(total_stats, text="單筆手續費: $0", style="Search.TLabel")
            self.total_sof_db.pack(side=LEFT, padx=20)
            self.total_avmoney = ttk.Label(total_stats, text="結算金額: $0", style="Search.TLabel")
            self.total_avmoney.pack(side=LEFT, padx=20)
            
            log_manager.debug("交易記錄界面UI初始化完成")
            
            # 初始化設備列表
            self.refresh_devices()
            
        except Exception as e:
            log_manager.exception("初始化交易記錄界面時發生錯誤")
            raise
            
    def set_loading(self, loading: bool):
        """設置加載狀態"""
        self.loading = loading
        if loading:
            self.search_button.pack_forget()
            self.export_button.pack_forget()
            self.loading_label.pack(side=LEFT)
        else:
            self.loading_label.pack_forget()
            self.search_button.pack(side=LEFT)
            self.export_button.pack(side=LEFT, padx=(10, 0))
            
    def _fetch_devices(self):
        """獲取設備列表（在後台線程中執行）"""
        try:
            response = requests.get(
                config_manager.get_api_url("/api/twd/member/devices"),
                headers={"Authorization": self.app_state.current_token}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return data.get("data", [])
                else:
                    raise Exception(data.get("message", "獲取設備列表失敗"))
            else:
                raise Exception(f"HTTP錯誤: {response.status_code}")
                
        except Exception as e:
            raise Exception(f"獲取設備列表失敗: {str(e)}")
            
    def _on_devices_loaded(self, devices):
        """設備列表加載完成的回調"""
        try:
            self.devices = devices  # 保存設備列表
            log_manager.info(f"成功獲取設備列表，共 {len(devices)} 個設備")
            self.device_combo["values"] = ["全部"] + [device["device_name"] for device in devices]
            self.device_combo.set("全部")
        except Exception as e:
            log_manager.exception("更新設備列表時發生錯誤")
            
    def _on_devices_error(self, error):
        """設備列表加載失敗的回調"""
        log_manager.error(f"獲取設備列表失敗：{str(error)}")
            
    def refresh_devices(self):
        """刷新設備列表"""
        log_manager.info("開始獲取設備列表")
        async_utils.run_async(
            self._fetch_devices,
            self._on_devices_loaded,
            self._on_devices_error
        )
            
    def get_date_range(self):
        today = datetime.now()
        if self.time_var.get() == "today":
            start = today.replace(hour=0, minute=0, second=0, microsecond=0)
            end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        elif self.time_var.get() == "yesterday":
            yesterday = today - timedelta(days=1)
            start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        elif self.time_var.get() == "this_week":
            start = today - timedelta(days=today.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
            end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        elif self.time_var.get() == "last_week":
            last_week = today - timedelta(weeks=1)
            start = last_week - timedelta(days=last_week.weekday())
            start = start.replace(hour=0, minute=0, second=0, microsecond=0)
            end = (start + timedelta(days=6)).replace(hour=23, minute=59, second=59, microsecond=999999)
        elif self.time_var.get() == "this_month":
            start = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end = today.replace(hour=23, minute=59, second=59, microsecond=999999)
        else:  # last_month
            first_day = today.replace(day=1)
            last_month = first_day - timedelta(days=1)
            start = last_month.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end = last_month.replace(day=calendar.monthrange(last_month.year, last_month.month)[1],
                                   hour=23, minute=59, second=59, microsecond=999999)
                                   
        return start.strftime("%Y-%m-%d %H:%M:%S"), end.strftime("%Y-%m-%d %H:%M:%S")
        
    def _fetch_transactions(self):
        """獲取交易記錄（在後台線程中執行）"""
        try:
            start_date, end_date = self.get_date_range()
            log_manager.debug(f"搜索時間範圍：{start_date} 至 {end_date}")
            
            # 獲取選擇的設備
            selected_device = self.device_var.get()
            device_id = None
            if selected_device != "全部":
                # 查找對應的設備ID
                device = next((d for d in self.devices if d["device_name"] == selected_device), None)
                if device:
                    device_id = device["id"]
            
            params = {
                "page": 1,
                "limit": 100,
                "bdate": start_date,
                "edate": end_date
            }
            
            # 如果選擇了特定設備，添加device_id參數
            if device_id is not None:
                params["device_id"] = device_id
            
            response = requests.get(
                config_manager.get_api_url("/api/twd/member/report"),
                headers={"Authorization": self.app_state.current_token},
                params=params
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return data
                else:
                    raise Exception(data.get("message", "獲取交易記錄失敗"))
            else:
                raise Exception(f"HTTP錯誤: {response.status_code}")
                
        except Exception as e:
            raise Exception(f"獲取交易記錄失敗: {str(e)}")
            
    def _on_transactions_loaded(self, data):
        """交易記錄加載完成的回調"""
        try:
            transactions = data.get("data", [])
            total_row = data.get("totalRow", {})
            
            # 保存當前查詢結果用於匯出
            self.current_transactions = transactions
            self.current_total_row = total_row
            
            log_manager.info(f"成功獲取交易記錄，共 {len(transactions)} 筆")
            
            # 清空現有數據
            for item in self.tree.get_children():
                self.tree.delete(item)
                
            for trans in transactions:
                # 查找設備名稱
                device_name = next(
                    (d["device_name"] for d in self.devices if d["bank_account"] == trans["bank_account"]),
                    trans["bank_account"]  # 如果找不到設備名稱，使用帳號作為備用
                )
                
                self.tree.insert("", END, values=(
                    trans["transaction_date"],
                    device_name,  # 使用設備名稱
                    f"${float(trans['money']):,.2f}",
                    trans["summary"],
                    f"${float(trans['sof']):,.2f}",
                    f"${float(trans['sof_db']):,.2f}",
                    f"${float(trans['avmoney']):,.2f}"
                ))
                
            # 更新統計信息
            self.total_money.config(text=f"總金額: ${float(total_row.get('money', 0)):,.2f}")
            self.total_sof.config(text=f"手續費: ${float(total_row.get('sof', 0)):,.2f}")
            self.total_sof_db.config(text=f"單筆手續費: ${float(total_row.get('sof_db', 0)):,.2f}")
            self.total_avmoney.config(text=f"結算金額: ${float(total_row.get('avmoney', 0)):,.2f}")
            
            # 啟用匯出按鈕
            self.export_button.configure(state="normal" if transactions else "disabled")
            
        except Exception as e:
            log_manager.exception("更新交易記錄時發生錯誤")
            Messagebox.show_error("更新交易記錄時發生錯誤", "錯誤")
        finally:
            self.set_loading(False)
            
    def _on_transactions_error(self, error):
        """交易記錄加載失敗的回調"""
        log_manager.error(f"獲取交易記錄失敗：{str(error)}")
        Messagebox.show_error("獲取交易記錄失敗", "錯誤")
        self.set_loading(False)
        
    def search_transactions(self):
        """搜索交易記錄"""
        if self.loading:
            return
            
        self.set_loading(True)
        log_manager.info("開始搜索交易記錄")
        
        # 異步加載交易記錄
        async_utils.run_async(
            self._fetch_transactions,
            self._on_transactions_loaded,
            self._on_transactions_error
        )

    def format_money(self, value):
        """格式化金額"""
        try:
            if pd.isna(value) or value == '':
                return ''
            # 如果是字串，先轉換為浮點數
            if isinstance(value, str):
                value = float(value.replace('$', '').replace(',', ''))
            return f'${value:,.2f}'
        except:
            return str(value)

    def get_column_width(self, text):
        """計算欄位寬度，考慮中文字符"""
        width = 0
        for char in str(text):
            # 如果是中文字符，寬度加 2，否則加 1
            if '\u4e00' <= char <= '\u9fff':
                width += 2
            else:
                width += 1
        return width

    def check_file_writable(self, file_path):
        """檢查檔案是否可寫入"""
        if not os.path.exists(file_path):
            # 檔案不存在，檢查目錄是否可寫入
            directory = os.path.dirname(file_path)
            return os.access(directory, os.W_OK)
        
        # 檔案存在，檢查是否可寫入
        try:
            with open(file_path, 'a'):
                return True
        except IOError:
            return False

    def export_transactions(self):
        """匯出交易記錄"""
        try:
            if not self.current_transactions:
                Messagebox.show_warning("沒有可匯出的資料", "警告")
                return

            # 取得檔案儲存位置
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv")],
                title="選擇匯出位置"
            )
            
            if not file_path:
                return
                
            # 準備交易記錄資料
            transactions_data = []
            for trans in self.current_transactions:
                device_name = next(
                    (d["device_name"] for d in self.devices if d["bank_account"] == trans["bank_account"]),
                    trans["bank_account"]
                )
                
                transactions_data.append({
                    "日期": trans["transaction_date"],
                    "設備": device_name,
                    "金額": float(trans["money"]),
                    "摘要": trans["summary"],
                    "手續費": float(trans["sof"]),
                    "單筆手續費": float(trans["sof_db"]),
                    "結算金額": float(trans["avmoney"])
                })
                
            # 創建 DataFrame
            df = pd.DataFrame(transactions_data)
            
            # 添加統計資訊
            stats_data = pd.DataFrame([{
                "日期": "統計資訊",
                "設備": "",
                "金額": float(self.current_total_row.get("money", 0)),
                "摘要": "",
                "手續費": float(self.current_total_row.get("sof", 0)),
                "單筆手續費": float(self.current_total_row.get("sof_db", 0)),
                "結算金額": float(self.current_total_row.get("avmoney", 0))
            }])
            
            # 合併交易記錄和統計資訊
            df = pd.concat([df, stats_data], ignore_index=True)
            
            try:
                # 根據檔案類型匯出
                if file_path.endswith('.xlsx'):
                    # 匯出到 Excel
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        df.to_excel(writer, index=False)
                        
                        # 獲取工作表
                        ws = writer.sheets['Sheet1']
                        
                        # 設定每個欄位的最佳寬度
                        for column in ws.columns:
                            max_length = 0
                            column_letter = get_column_letter(column[0].column)
                            
                            # 檢查每個儲存格的內容長度，包括標題
                            for cell in column:
                                try:
                                    length = self.get_column_width(str(cell.value))
                                    if length > max_length:
                                        max_length = length
                                except:
                                    pass
                            
                            # 設定欄寬（加上邊距）
                            adjusted_width = (max_length + 4)
                            ws.column_dimensions[column_letter].width = adjusted_width
                        
                        # 設定金額欄位格式
                        money_columns = ['金額', '手續費', '單筆手續費', '結算金額']
                        for col in money_columns:
                            col_idx = df.columns.get_loc(col) + 1  # Excel 欄位從 1 開始
                            for row in range(2, len(df) + 2):  # Excel 列從 2 開始（跳過標題）
                                cell = ws.cell(row=row, column=col_idx)
                                cell.number_format = '$#,##0.00'
                else:
                    df.to_csv(file_path, index=False)
                    
                log_manager.info(f"成功匯出交易記錄到：{file_path}")
                Messagebox.show_info("匯出成功", "提示")
                
            except PermissionError:
                Messagebox.show_error(
                    "無法寫入檔案，請確認檔案未被其他程式開啟。",
                    "錯誤"
                )
            except Exception as e:
                log_manager.exception("匯出交易記錄時發生錯誤")
                Messagebox.show_error(f"匯出失敗：{str(e)}", "錯誤")
            
        except Exception as e:
            log_manager.exception("匯出交易記錄時發生錯誤")
            Messagebox.show_error(f"匯出失敗：{str(e)}", "錯誤")

