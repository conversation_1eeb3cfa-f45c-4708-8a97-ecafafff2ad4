import os
from pathlib import Path
from dotenv import load_dotenv, find_dotenv, dotenv_values
from gui.utils.logger import log_manager

class ConfigManager:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
        
    def _initialize(self):
        """初始化配置管理器"""
        try:
            # 獲取.env文件的絕對路徑
            env_path = Path(__file__).parent.parent.parent / '.env'
            log_manager.debug(f"嘗試加載環境變數文件: {env_path}")
            
            # 確認文件存在
            if not env_path.exists():
                raise FileNotFoundError(f"找不到 .env 文件: {env_path}")
            
            # 直接讀取 .env 文件內容
            env_values = dotenv_values(env_path)
            # log_manager.debug(f"環境變數文件內容: {env_values}")
            
            # 強制重新加載環境變數
            load_dotenv(env_path, override=True)
            
            # 獲取並驗證 API_BASE_URL
            self.api_base_url = os.getenv('API_BASE_URL')
            # log_manager.debug(f"讀取到的 API_BASE_URL: {self.api_base_url}")
            
            if not self.api_base_url:
                raise ValueError("環境變數 API_BASE_URL 未設置")
                
        except Exception as e:
            log_manager.error(f"初始化配置管理器時發生錯誤: {str(e)}")
            raise
        
    def get_api_url(self, endpoint):
        """獲取完整的API URL"""
        # 每次獲取 URL 時重新讀取環境變數
        env_path = Path(__file__).parent.parent.parent / '.env'
        if env_path.exists():
            load_dotenv(env_path, override=True)
            self.api_base_url = os.getenv('API_BASE_URL')
            if not self.api_base_url:
                raise ValueError("環境變數 API_BASE_URL 未設置")
        
        full_url = f"{self.api_base_url}{endpoint}"
        # log_manager.debug(f"生成完整 API URL: {full_url}")
        return full_url
        
    def reload_config(self):
        """重新加載配置"""
        self._initialize()

# 創建全局配置管理器實例
config_manager = ConfigManager()
