"""
將來銀行操作模組 (Playwright 版本)
實作將來銀行特定的自動化操作
"""
import time
import json
from playwright.sync_api import TimeoutError
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class NextBank(BankBase):
    """將來銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        super().__init__(
            bank_code="823",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = "https://accessibility.nextbank.com.tw/"
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        self.last_action_time = 0  # 上次操作時間
        self.action_interval = 300  # 操作間隔（秒）
        self.stored_cookies = None  # 儲存已獲取的 cookies
        self.token_requested = False  # 追踪是否已請求過 token

    def _handle_request(self, request):
        """處理請求事件，獲取 Acstkn token"""
        if not self.is_running or not self.token_requested or self.stored_cookies:
            return
            
        try:
            # 檢查是否為目標 API 請求
            if 'api.nextbank.com.tw/ap1/api/v3.0/AppMainPage/CurrentDepositDetail' in request.url and request.method == 'POST':
                headers = request.headers
                acstkn = headers.get('acstkn')
                
                if acstkn:
                    # 儲存 token
                    self.stored_cookies = {"Acstkn": acstkn}
                    
                    # 更新到 API
                    if self.update_cookies(self.stored_cookies):
                        self.cookies_updated = True
                        self.last_cookie_update = time.time()
                        log_manager.info("成功獲取並更新 token", device=self.device_id)
                        
        except Exception as e:
            log_manager.error(f"處理請求時發生錯誤: {str(e)}", device=self.device_id)

    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 檢查主帳戶明細查詢按鈕
            detail_button = self.page.get_by_label("主帳戶明細查詢")
            if detail_button.is_visible():
                detail_button.click()
                log_manager.debug("點擊主帳戶明細查詢按鈕", device=self.device_id)
                
                # 等待近30天按鈕出現 (最多 3 次嘗試，每次間隔 5 秒)
                days_30_button = self.page.get_by_label("近30天")
                max_retries = 3
                retry_delay = 5
                button_found = False

                for attempt in range(max_retries):
                    if not self.is_running: return False # 每次重試前檢查

                    log_manager.debug(f"檢查 '近30天' 按鈕可見性 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                    try:
                        days_30_button.wait_for(state="visible", timeout=5000)
                        button_found = True
                        log_manager.debug("'近30天' 按鈕已可見", device=self.device_id)
                        break # 找到按鈕，跳出重試循環

                    except TimeoutError:
                        log_manager.warning(f"第 {attempt + 1} 次嘗試未找到 '近30天' 按鈕或超時", device=self.device_id)
                        if attempt < max_retries - 1:
                            log_manager.info(f"等待 {retry_delay} 秒後重試...", device=self.device_id)
                            for _ in range(retry_delay):
                                if not self.is_running: return False
                                time.sleep(1)
                        else:
                            # 最後一次嘗試失敗
                            self.log_with_screenshot('error', f"登入檢查失敗：重試 {max_retries} 次後仍未找到 '近30天' 按鈕", "login_check_retries_failed")
                            return False # 返回 False 表示檢查失敗
                    except Exception as inner_e:
                        # 捕獲 wait_for 期間的其他潛在錯誤
                        log_manager.error(f"等待 '近30天' 按鈕時發生未預期錯誤 (嘗試 {attempt + 1}): {str(inner_e)}", device=self.device_id)
                        if attempt < max_retries - 1:
                            log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                            for _ in range(retry_delay):
                                if not self.is_running: return False
                                time.sleep(1)
                        else:
                            self.log_with_screenshot('error', f"等待 '近30天' 按鈕時發生錯誤且已達最大重試次數: {str(inner_e)}", "login_check_error_final")
                            return False

                # --- 循環結束後 ---
                if button_found:
                    # 設置 token_requested 標記
                    self.token_requested = True

                    # 點擊近30天按鈕
                    days_30_button.click()
                    log_manager.debug("點擊近30天按鈕", device=self.device_id)

                    # 等待 API 請求完成
                    try:
                        self.page.wait_for_load_state("networkidle", timeout=15000) # 增加等待時間
                    except TimeoutError:
                         self.log_with_screenshot('warning', "等待 networkidle 超時，可能未完全捕獲 token", "networkidle_timeout_token")
                         # 即使超時，也繼續嘗試檢查 token

                    self.token_requested = False

                    if not self.stored_cookies:
                        self.log_with_screenshot('error',
                            "無法獲取初始 token (點擊 '近30天' 後)",
                            "initial_token_error")
                        return False

                    self.last_action_time = time.time()
                    return True
                else:
                    # 如果按鈕最終未找到
                    log_manager.error("登入檢查最終失敗 (未找到 '近30天' 按鈕)", device=self.device_id)
                    return False
                    
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
                self.stop()
            return False

    def perform_periodic_tasks(self):
        """執行定時任務：更新 cookies 和點擊保持登入"""
        try:
            current_time = time.time()
            
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update >= self.cookie_update_interval:
                if self.stored_cookies and self.update_cookies(self.stored_cookies):
                    self.last_cookie_update = current_time
                    log_manager.info("成功發送 token", device=self.device_id)
                    
            # 檢查是否需要點擊保持登入
            if current_time - self.last_action_time >= self.action_interval:
                # 點擊任意位置即可
                self.page.mouse.click(100, 100)
                self.last_action_time = current_time
                log_manager.debug("點擊保持登入", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"執行定時任務時發生錯誤: {str(e)}", device=self.device_id)

    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待並獲取輸入欄位
            try:
                # 等待身分證字號欄位出現
                try:
                    id_input = self.page.get_by_label("身分證字號")
                    id_input.wait_for(state="visible", timeout=3000)
                except TimeoutError:
                    log_manager.debug("自動填入失敗：等待身分證字號欄位超時", device=self.device_id)
                    return False
                
                # 使用 label 定位輸入欄位
                user_id_input = self.page.get_by_label("使用者代號", exact=True)
                password_input = self.page.get_by_label("使用者密碼")
                
                # 確保所有欄位都可見
                if not all([id_input.is_visible(), user_id_input.is_visible(), password_input.is_visible()]):
                    log_manager.debug("自動填入失敗：部分輸入欄位未顯示", device=self.device_id)
                    return False
                    
            except Exception as e:
                log_manager.debug(f"自動填入失敗：等待輸入欄位超時 - {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 將來銀行專用選擇器 - 基於實際頁面結構
                    captcha_selectors = [
                        'img[alt="驗證碼圖片"]',  # 將來銀行專用 - 精確選擇器
                        'img[src*="captcha"]',  # 通用驗證碼圖片
                        'img[src*="verify"]',
                        'img[src*="code"]',
                        'img[src*="data:image"]',  # base64 圖片
                        'img[alt*="驗證"]',
                        'img[title*="驗證"]'
                    ]
                    input_selectors = [
                        'textbox[name="圖形驗證碼"]',  # 將來銀行專用 - role-based 選擇器
                        'input[placeholder*="驗證"]',
                        'input[name*="captcha"]',
                        'input[name*="verify"]',
                        'input[id*="captcha"]',
                        'input[id*="verify"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False

    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟將來銀行網頁...")
        
        # 設定請求監聽
        self.page.on("request", self._handle_request)
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 開啟登入頁面
                self.page.goto(self.login_url, wait_until="networkidle")
                
                # 檢查並點擊「了解」按鈕 - 改進處理邏輯
                try:
                    understand_button = self.page.get_by_label("了解，前往功能清單")
                    understand_button.wait_for(state="visible", timeout=3000)
                    understand_button.click()
                    log_manager.debug("點擊了解按鈕", device=self.device_id)
                    # 等待頁面跳轉
                    time.sleep(2)
                except TimeoutError:
                    log_manager.debug("未找到了解按鈕，可能已在功能清單頁面", device=self.device_id)
                except Exception as e:
                    log_manager.warning(f"處理了解按鈕時發生錯誤: {str(e)}", device=self.device_id)

                # 點擊新臺幣帳戶總覽
                try:
                    overview_button = self.page.get_by_role("button", name="新臺幣帳戶總覽")
                    overview_button.wait_for(state="visible", timeout=5000)
                    overview_button.click()
                    log_manager.debug("點擊新臺幣帳戶總覽按鈕", device=self.device_id)
                    # 等待登入頁面載入
                    time.sleep(2)
                except TimeoutError:
                    log_manager.error("未找到新臺幣帳戶總覽按鈕", device=self.device_id)
                    continue
                except Exception as e:
                    log_manager.error(f"點擊新臺幣帳戶總覽按鈕時發生錯誤: {str(e)}", device=self.device_id)
                    continue
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                self.update_status("登入成功，開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    try:
                        # 執行定時任務
                        self.perform_periodic_tasks()
                        
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"執行操作時發生錯誤: {str(e)}",
                                "operation_error")
                            self.update_status("發生錯誤，等待重試...")
                            time.sleep(10)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    self.update_status("發生錯誤，等待重試...")
                    time.sleep(10)
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None

    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止將來銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器