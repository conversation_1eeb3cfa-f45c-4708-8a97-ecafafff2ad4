"""
樂天銀行操作模組 (Playwright 版本)
實作樂天銀行特定的自動化操作
"""

import re
import os
import sys
import time
import json
import subprocess
from datetime import datetime
from pathlib import Path
from playwright.sync_api import TimeoutError, expect, sync_playwright
from gui.utils.logger import log_manager
import psutil # <--- 新增導入 psutil
from pathlib import Path # <--- 新增導入 Path
from .base_playwright import BankBase
from proxy_scripts.start_proxy import get_lock_file_path


class RakutenBank(BankBase):
    """樂天銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=False, use_proxy: bool = False): # 新增 is_mobile 和 use_proxy 參數
        # 將 use_proxy 傳遞給 BankBase，讓其決定是否生成外部 proxy 設定
        super().__init__(
            bank_code='826',  # 樂天銀行代碼
            bank_account=str(bank_account),  # 確保帳號是字串
            device_id=device_id,
            device_data=device_data,
            is_mobile=is_mobile,  # 支援手機版設定
            use_proxy=use_proxy # 傳遞 use_proxy 狀態
            # 不再直接設定 Playwright 的 proxy，流量將通過 mitmproxy
        )
        # Playwright 的 context proxy 將設定為 mitmproxy
        self.mitm_port = None # 用於存儲動態分配的端口
        self.proxy_process = None  # 存儲代理進程
        self.mitm_pid = None # 儲存 mitmproxy 的 PID
        self.login_url = 'https://www.rakuten-bank.com.tw/ebank/cgn/cgnot0001/010'
        self.stored_rqdata = None  # 存儲 rqData
        self.last_check_time = time.time()  # 上次檢查時間
        self.check_interval = 60  # 檢查間隔（秒）
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        
    def get_initial_rqdata(self):
        """獲取初始 rqData"""
        if not self.is_running:
            return False
            
        try:
            # 點擊下載圖標觸發 Fiddler 腳本
            download_icon = self.page.locator(".td > .square_icon").first
            download_icon.click()
            
            # 等待下載對話框並點擊確認
            with self.page.expect_download() as download_info:
                with self.page.expect_popup() as page1_info:
                    self.page.get_by_text("確認下載").click()
                popup = page1_info.value
            download = download_info.value
            
            # 取消下載並關閉彈出視窗
            if download:
                download.cancel()
                log_manager.debug("已取消下載", device=self.device_id)
            
            if popup:
                popup.close()
                log_manager.debug("已關閉彈出視窗", device=self.device_id)
            
            # 從 window.pppp 取得 rqData
            try:
                rqData = self.page.evaluate("window.pppp")
                if rqData:
                    log_manager.debug("成功取得初始 rqData", device=self.device_id)
                    self.stored_rqdata = rqData
                    return True
                else:
                    log_manager.error("無法從 window.pppp 取得初始 rqData", device=self.device_id)
                    return False
            except Exception as e:
                log_manager.error(f"取得初始 rqData 時發生錯誤: {str(e)}", device=self.device_id)
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"觸發 Fiddler 腳本時發生錯誤: {str(e)}",
                    "fiddler_trigger_error")
            return False
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies"""
        if not self.is_running:
            return
            
        try:
            current_time = time.time()
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update < self.cookie_update_interval:
                return
                
            # 如果還沒有 rqData，獲取初始值
            if not self.stored_rqdata and not self.get_initial_rqdata():
                return
                
            # 獲取當前頁面的所有 cookies
            all_cookies = self.page.context.cookies()
            
            # 將 cookies 組合成字串
            cookies_string = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in all_cookies])
            
            # 使用已存儲的 rqData 和 cookies 更新到 API
            cookies_data = {
                "rqData": self.stored_rqdata,
                "Cookie": cookies_string
            }
            
            if self.update_cookies(cookies_data):
                log_manager.debug("成功更新 rqData 到 API", device=self.device_id)
                self.last_cookie_update = current_time
                self.update_status("已更新 rqData")
            else:
                log_manager.error("更新 rqData 到 API 失敗", device=self.device_id)
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"更新 cookies 時發生錯誤: {str(e)}",
                    "update_cookies_error")

    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 檢查存款連結是否出現
            deposit_link = self.page.get_by_role("link", name="存款", exact=True)
            if deposit_link.is_visible():
                # 處理歡迎提示
                try:
                    # 檢查歡迎提示是否出現
                    welcome_text = self.page.locator("div").filter(
                        has_text=re.compile(r"^樂天好朋友，完成任務老朋友享NT\$500推薦禮\(無上限\)，新朋友也享NT\$500歡迎禮$")
                    )
                    
                    if welcome_text.is_visible():
                        # 點擊不再顯示按鈕
                        self.page.get_by_role("button", name="不再顯示").click()
                        log_manager.debug("處理了歡迎提示視窗", device=self.device_id)
                except Exception:
                    # 忽略歡迎提示處理的錯誤
                    pass
                    
                log_manager.debug("登入狀態確認成功", device=self.device_id)
                return True
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
                self.stop()
            return False
            
    def verify_account(self):
        """驗證帳號"""
        if not self.is_running:
            return False
            
        try:
            # 等待並點擊箭頭圖標
            arrow_link = self.page.locator("div.ml-auto > a")
            # 增加箭頭圖標的等待時間，確保它確實可見可點擊
            arrow_link.wait_for(state="visible", timeout=20000)
            log_manager.debug("準備點擊箭頭圖標進入帳戶頁面", device=self.device_id)
            arrow_link.click()
            
            # 移除 networkidle 等待，直接等待關鍵元素
            # self.page.wait_for_load_state("networkidle")
            
            # 等待並確認進入交易明細頁面
            # 增加 heading 的等待時間，以覆蓋頁面加載
            heading = self.page.get_by_role("heading", name="臺幣存款 活存 定存")
            log_manager.debug("等待交易明細頁面標題 '臺幣存款 活存 定存' 出現...", device=self.device_id)
            heading.wait_for(state="visible", timeout=30000) # 從 10s 增加到 30s
            log_manager.debug("已進入交易明細頁面", device=self.device_id)
            
            # --- 帳號驗證重試邏輯 ---
            max_retries = 3
            retry_delay = 5
            account_verified = False
            account_element_locator = self.page.locator("a.txt_dropdown.notab[data-toggle='dropdown']")

            for attempt in range(max_retries):
                if not self.is_running: return False # 每次重試前檢查

                log_manager.debug(f"開始驗證樂天帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                try:
                    # 等待帳號元素出現，這裡的超時可以保持 10 秒，因為頁面應該已經加載
                    account_element_locator.wait_for(state="visible", timeout=15000) # 從 10s 增加到 15s
                    account_text = account_element_locator.inner_text()
                    log_manager.debug(f"找到帳號文字: {account_text} (嘗試 {attempt + 1})", device=self.device_id)

                    # 從 "活存總額 XXXX" 格式中提取帳號
                    account_number_match = re.search(r'活存總額\s+(\d+)', account_text)

                    if not account_number_match:
                        log_manager.warning(f"第 {attempt + 1} 次嘗試無法從文字中提取帳號: {account_text}", device=self.device_id)
                        # 提取失敗，進入重試邏輯
                    else:
                        extracted_account = account_number_match.group(1)
                        if extracted_account == self.bank_account:
                            log_manager.info(f"帳號驗證成功: {self.bank_account} (嘗試 {attempt + 1})", device=self.device_id)
                            account_verified = True
                            break # 驗證成功，跳出重試循環
                        else:
                            log_manager.warning(f"第 {attempt + 1} 次嘗試帳號不一致，預期: {self.bank_account}，實際: {extracted_account}", device=self.device_id)
                            # 帳號不符，進入重試邏輯 (雖然通常帳號不符應該直接失敗，但按要求加入重試)

                    # --- 重試處理 ---
                    if not account_verified and attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    elif not account_verified and attempt == max_retries - 1:
                        # 最後一次嘗試失敗 (提取失敗或帳號不符)
                        error_reason = "account_extract_error_retries_failed" if not account_number_match else "account_mismatch_retries_failed"
                        error_message = f"帳號驗證失敗：重試 {max_retries} 次後仍無法驗證帳號 {self.bank_account}"
                        if account_number_match:
                             error_message += f" (最後提取到: {extracted_account})"
                        self.log_with_screenshot('error', error_message, error_reason)
                        self.stop() # 驗證失敗，停止
                        return False

                except TimeoutError:
                    log_manager.warning(f"第 {attempt + 1} 次嘗試等待帳號元素超時", device=self.device_id)
                    if attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    else:
                        self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍找不到帳號元素", "account_element_timeout_retries_failed")
                        self.stop()
                        return False
                except Exception as inner_e:
                    log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(inner_e)}", device=self.device_id)
                    if attempt < max_retries - 1:
                        log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    else:
                        self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(inner_e)}", "account_verify_error_final")
                        self.stop()
                        return False

            # --- 循環結束後 ---
            if account_verified:
                return True
            else:
                # 理論上如果失敗會在循環內 return False
                log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
                if self.is_running: self.stop() # 確保停止
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"驗證帳號時發生錯誤: {str(e)}",
                    "account_verify_error")
                self.stop()
            return False
            
    def check_and_handle_logout_notice(self):
        """檢查並處理登出提醒視窗"""
        if not self.is_running:
            return

        try:
            # 檢查是否出現登出標題
            logout_heading = self.page.get_by_role("heading", name="登出網路銀行")
            if logout_heading.is_visible():
                # 點擊繼續使用按鈕
                continue_button = self.page.locator("timeout-modal").get_by_text("繼續使用")
                continue_button.wait_for(state="visible", timeout=5000)
                continue_button.click()
                log_manager.debug("處理了登出提醒視窗", device=self.device_id)

        except Exception as e:
            # 忽略錯誤，因為這是持續監控的操作
            pass

    def handle_post_login_popups(self):
        """處理登入後的各種彈窗 - 優化版本"""
        if not self.is_running:
            return

        try:
            # 等待頁面穩定
            time.sleep(2)

            # 定義多種彈窗關閉策略
            popup_strategies = [
                # 策略1: 推薦好友彈窗 - 使用關閉按鈕
                {
                    'name': '推薦好友彈窗',
                    'selectors': [
                        'button[aria-label="關閉"]',
                        'button:has-text("關閉")',
                        '[role="button"]:has-text("關閉")'
                    ]
                },
                # 策略2: 推薦好友彈窗 - 使用不再顯示按鈕
                {
                    'name': '推薦好友彈窗(不再顯示)',
                    'selectors': [
                        'button:has-text("不再顯示")',
                        '[role="button"]:has-text("不再顯示")'
                    ]
                },
                # 策略3: 通用彈窗關閉按鈕
                {
                    'name': '通用彈窗',
                    'selectors': [
                        '[class*="modal"] button[class*="close"]',
                        '[class*="popup"] button[class*="close"]',
                        '[class*="dialog"] button[class*="close"]',
                        'button[class*="close"]'
                    ]
                },
                # 策略4: ESC 鍵關閉
                {
                    'name': 'ESC鍵關閉',
                    'action': 'keyboard',
                    'key': 'Escape'
                }
            ]

            # 嘗試每種策略
            for strategy in popup_strategies:
                if not self.is_running:
                    break

                try:
                    if strategy.get('action') == 'keyboard':
                        # 鍵盤操作
                        self.page.keyboard.press(strategy['key'])
                        log_manager.debug(f"使用 {strategy['name']} 策略 (按鍵: {strategy['key']})", device=self.device_id)
                        time.sleep(1)
                    else:
                        # 點擊操作
                        for selector in strategy['selectors']:
                            try:
                                element = self.page.locator(selector).first
                                if element.is_visible(timeout=1000):
                                    element.click()
                                    log_manager.info(f"成功關閉彈窗 - {strategy['name']} (選擇器: {selector})", device=self.device_id)
                                    time.sleep(1)
                                    return  # 成功關閉，退出
                            except:
                                continue

                except Exception as e:
                    log_manager.debug(f"{strategy['name']} 策略失敗: {str(e)}", device=self.device_id)
                    continue

            log_manager.debug("彈窗處理完成", device=self.device_id)

        except Exception as e:
            log_manager.warning(f"處理登入後彈窗時發生錯誤: {str(e)}", device=self.device_id)
            
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 直接定位並填入資料
                id_input = self.page.get_by_role("textbox", name="身分證字號")
                user_id_input = self.page.get_by_role("textbox", name="使用者代號")
                password_input = self.page.get_by_label("登入密碼")
                
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 樂天銀行專用選擇器 - 根據實際頁面結構優化
                    captcha_selectors = [
                        # 樂天銀行專用 - 基於實際 DOM 結構的精確選擇器
                        'captcha-image img',  # 直接定位驗證碼組件內的圖片
                        'form div[class*="captcha"] img',  # 表單內驗證碼區域的圖片
                        'img[src*="data:image"]',  # base64 內嵌圖片
                        'img[src*="captcha"]',  # 通用驗證碼圖片
                        'img[src*="verify"]',
                        'img[src*="code"]',
                        'img'  # 通用圖片選擇器（最後備用）
                    ]
                    input_selectors = [
                        'textbox[placeholder="驗證碼"]',  # 樂天銀行專用 - role-based 選擇器
                        'input[placeholder="驗證碼"]',  # 樂天銀行專用 - 傳統選擇器
                        'input[name*="captcha"]',
                        'input[placeholder*="驗證"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
                
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start_proxy(self, upstream_proxy=None):
        """啟動 mitmproxy，並可選擇性地設定上游代理"""
        try:
            # 獲取代理腳本路徑
            if getattr(sys, 'frozen', False):
                # 如果是打包後的執行檔
                if hasattr(sys, '_MEIPASS'):
                    # 使用 PyInstaller 的臨時目錄
                    base_path = sys._MEIPASS
                else:
                    base_path = os.path.dirname(sys.executable)
            else:
                # 如果是開發環境
                base_path = os.path.dirname(os.path.dirname(__file__))
                
            # 設置代理腳本路徑
            proxy_dir = os.path.join(base_path, 'proxy_scripts')
            log_manager.debug(f"代理腳本目錄: {proxy_dir}", device=self.device_id)
            
            # 添加到 Python 路徑
            if proxy_dir not in sys.path:
                sys.path.insert(0, proxy_dir)
                
            if base_path not in sys.path:
                sys.path.insert(0, base_path)
                
            # 直接執行代理腳本
            sys.path.insert(0, proxy_dir)
            try:
                from proxy_scripts.start_proxy import start_mitmproxy
            except ImportError:
                from start_proxy import start_mitmproxy
            
            # 啟動代理
            # 將上游代理設定傳遞給啟動函數，並接收返回的進程、端口號和 PID
            self.proxy_process, self.mitm_port, self.mitm_pid = start_mitmproxy(device_id=self.device_id, upstream_proxy=upstream_proxy)
            if not self.proxy_process or not self.mitm_port or not self.mitm_pid:
                log_manager.error("代理啟動失敗、未分配端口或未獲取 PID", device=self.device_id)
                return False
                
            log_manager.info("代理啟動成功", device=self.device_id)
            time.sleep(2)  # 等待代理啟動
            return True
        except Exception as e:
            log_manager.error(f"啟動代理時發生錯誤: {str(e)}", device=self.device_id)
            return False

    def start(self):
        """開始執行"""
        self.is_running = True
        
        # 啟動代理，並傳入 BankBase 生成的外部代理設定 (self.proxy)
        if not self.start_proxy(upstream_proxy=self.proxy):
            self.stop()
            return
            
        # 檢查 mitm_port 是否已分配
        if not self.mitm_port:
             log_manager.error("mitmproxy 端口未分配，無法設定瀏覽器代理", device=self.device_id)
             self.stop()
             return

        # 在 setup_browser 之前，使用動態端口設定 Playwright 的代理
        playwright_proxy_setting = {'server': f'http://127.0.0.1:{self.mitm_port}'}
        original_proxy_setting = self.proxy # 暫存 BankBase 的 proxy 設定
        self.proxy = playwright_proxy_setting # 設定 Playwright 連接到 mitmproxy
        self.setup_browser()
        self.proxy = original_proxy_setting # 恢復 BankBase 的 proxy 設定，以備他用
        self.update_status("正在開啟樂天銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 訪問登入頁面，使用 domcontentloaded 事件
                self.page.goto(self.login_url, wait_until="domcontentloaded")
                
                # 立即嘗試自動填入，不等待其他資源載入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                        
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                # 處理登入後彈窗
                self.update_status("處理登入後彈窗...")
                self.handle_post_login_popups()

                # 驗證帳號
                self.update_status("登入成功，驗證帳號...")
                if not self.verify_account():
                    break  # verify_account 會自動呼叫 stop()

                self.update_status("帳號驗證成功，開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    # 檢查並處理登出提醒視窗
                    self.check_and_handle_logout_notice()
                    
                    # 更新 cookies
                    self.get_and_update_cookies()
                    
                    # 短暫休息以減少 CPU 使用
                    time.sleep(1)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
                
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止樂天銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
        
        # 停止代理並清理鎖定文件
        if self.mitm_pid: # 使用 PID 來判斷是否有進程需要處理
            try:
                log_manager.debug(f"嘗試停止 mitmproxy 進程 (PID: {self.mitm_pid})", device=self.device_id)
                # 檢查進程是否存在再嘗試終止
                if psutil.pid_exists(self.mitm_pid):
                     process_to_kill = psutil.Process(self.mitm_pid)
                     process_to_kill.terminate()
                     try:
                         process_to_kill.wait(timeout=5) # 等待進程結束
                         log_manager.info(f"mitmproxy 進程 (PID: {self.mitm_pid}) 已停止", device=self.device_id)
                     except psutil.TimeoutExpired:
                         log_manager.warning(f"等待 mitmproxy 進程 (PID: {self.mitm_pid}) 結束超時，嘗試強制終止", device=self.device_id)
                         process_to_kill.kill()
                         process_to_kill.wait(timeout=5) # 等待強制終止
                         log_manager.info(f"mitmproxy 進程 (PID: {self.mitm_pid}) 已強制停止", device=self.device_id)
                else:
                     log_manager.debug(f"mitmproxy 進程 (PID: {self.mitm_pid}) 在嘗試停止前已不存在", device=self.device_id)

            except psutil.NoSuchProcess:
                 log_manager.debug(f"mitmproxy 進程 (PID: {self.mitm_pid}) 在嘗試停止前已不存在", device=self.device_id)
            except Exception as e:
                log_manager.error(f"停止 mitmproxy 進程 (PID: {self.mitm_pid}) 時發生錯誤: {str(e)}", device=self.device_id)

        # 刪除鎖定文件 (即使進程停止失敗也要嘗試刪除)
        if self.mitm_port:
            # 使用導入的函數獲取正確的絕對鎖定文件路徑
            lock_file_path = get_lock_file_path(self.mitm_port)
            try:
                if lock_file_path.exists():
                    lock_file_path.unlink()
                    log_manager.info(f"已刪除鎖定文件: {lock_file_path.name}", device=self.device_id)
            except Exception as e:
                log_manager.error(f"刪除鎖定文件 {lock_file_path.name} 時發生錯誤: {e}", device=self.device_id)

        self.proxy_process = None
        self.mitm_port = None
        self.mitm_pid = None
