import tkinter as tk
from tkinter import ttk
from ttkbootstrap.constants import *
from gui.utils.logger import log_manager
from bank.banks import bank_registry

class DeviceCard(ttk.Frame):
    """單個設備卡片元件"""
    def __init__(self, parent, device_data, on_login, on_stop):
        super().__init__(parent)
        
        self.device_data = device_data
        self.on_login = on_login
        self.on_stop = on_stop
        self.tooltip = None
        self.widgets = {}  # 存儲需要更新的小部件
        self.edit_enabled = self.device_data["enabled"]  # 追蹤編輯按鈕是否可用
        
        # 設置卡片樣式
        self.configure(
            style="Card.TFrame",
            padding=15,
            relief="solid",
            borderwidth=1
        )
        
        self._init_ui()
        
        # 載入設定
        self.load_settings()
        
    def _init_ui(self):
        """初始化卡片UI"""
        # 設備名稱和狀態行
        header_frame = ttk.Frame(self)
        header_frame.pack(fill=X, expand=True)
        
        # 設備名稱
        # 根據 account_type 顯示對應文字
        account_type_text = "(個人)" if self.device_data.get("account_type", 1) == 1 else "(公司)"
        self.widgets['device_name'] = ttk.Label(
            header_frame,
            text=f"{self.device_data['device_name']}{account_type_text}",
            font=("Noto Sans TC", 14, "bold"),
            bootstyle="primary"
        )
        self.widgets['device_name'].pack(side=LEFT)
        
        # 編輯按鈕（使用標籤樣式）
        self.edit_button = ttk.Label(
            header_frame,
            text="編輯",
            bootstyle="info-inverse",
            padding=(10, 2),
            cursor="hand2" if self.edit_enabled else ""
        )
        self.edit_button.pack(side=RIGHT, padx=(0, 10))
        self.edit_button.bind('<Button-1>', self._handle_edit_click)
        
        # 狀態標籤
        status_style = "success" if self.device_data["status"] == 1 else "danger"
        status_text = "在線" if self.device_data["status"] == 1 else "離線"
        self.widgets['status'] = ttk.Label(
            header_frame,
            text=status_text,
            bootstyle=f"{status_style}-inverse",
            padding=(10, 2)
        )
        self.widgets['status'].pack(side=RIGHT)
        
        # 銀行信息
        bank_frame = ttk.Frame(self)
        bank_frame.pack(fill=X, pady=(10, 5))
        
        # 使用 bank_registry 獲取銀行名稱
        bank_info = bank_registry.get_bank_by_code(self.device_data["bank_code"])
        bank_name = bank_info.name if bank_info else "未知銀行"
        self.widgets['bank'] = ttk.Label(
            bank_frame,
            text=f"銀行：{bank_name}",
            font=("Noto Sans TC", 11)
        )
        self.widgets['bank'].pack(side=LEFT)
        
        # 帳號信息
        account_frame = ttk.Frame(self)
        account_frame.pack(fill=X, pady=5)

        self.widgets['account'] = ttk.Label(
            account_frame,
            text=f"帳號：{self.device_data['bank_account']}",
            font=("Noto Sans TC", 11)
        )
        self.widgets['account'].pack(side=LEFT)

        # 檢查是否可以啟用自動填入
        can_auto_fill = all(
            self.device_data.get(field) 
            for field in ['bank_id_number', 'bank_user_id', 'bank_user_password']
        )
        
        # 設定選項 (自動填入、代理)
        options_frame = ttk.Frame(self)
        options_frame.pack(fill=X, pady=5)

        # 自動填入核取方塊 (移到 options_frame 並修改樣式)
        self.auto_fill_var = tk.BooleanVar(value=False)
        self.widgets['auto_fill'] = ttk.Checkbutton(
            options_frame, # 改為 options_frame
            text="自動填入",
            variable=self.auto_fill_var,
            command=self._handle_auto_fill_change,
            state="normal" if can_auto_fill else "disabled",
            bootstyle="primary-round-toggle" # 修改樣式
        )
        self.widgets['auto_fill'].pack(side=LEFT, padx=(0, 10)) # 靠左排列，增加右邊間距

        # 代理設定 (移到 options_frame)
        self.use_proxy_var = tk.BooleanVar(value=False)
        self.widgets['use_proxy'] = ttk.Checkbutton(
            options_frame, # 改為 options_frame
            text="使用代理",
            variable=self.use_proxy_var,
            command=self._handle_proxy_change,
            bootstyle="primary-round-toggle" # 使用圓形切換按鈕樣式
        )
        self.widgets['use_proxy'].pack(side=LEFT) # 靠左排列


        # 當月限額信息
        monthly_frame = ttk.Frame(self)
        monthly_frame.pack(fill=X, pady=5)

        monthly_limit = float(self.device_data['monthly_limit'])
        monthly_remaining = float(self.device_data['remaining_monthly_limit'])
        self.widgets['monthly'] = ttk.Label(
            monthly_frame,
            text=f"當月剩餘/限額：{monthly_remaining:,.0f}/{monthly_limit:,.0f}",
            font=("Noto Sans TC", 11)
        )
        self.widgets['monthly'].pack(side=LEFT)

        
        # 當日限額信息
        daily_frame = ttk.Frame(self)
        daily_frame.pack(fill=X, pady=5)
        
        daily_limit = float(self.device_data['daily_limit'])
        daily_remaining = float(self.device_data['remaining_daily_limit'])
        self.widgets['daily'] = ttk.Label(
            daily_frame,
            text=f"當日剩餘/限額：{daily_remaining:,.0f}/{daily_limit:,.0f}",
            font=("Noto Sans TC", 11)
        )
        self.widgets['daily'].pack(side=LEFT)
        
        # 結算金額信息
        avmoney_frame = ttk.Frame(self)
        avmoney_frame.pack(fill=X, pady=5)
        
        avmoney = float(self.device_data.get('avmoney', '0.00'))
        self.widgets['avmoney'] = ttk.Label(
            avmoney_frame,
            text=f"結算金額：{avmoney:,.2f}",
            font=("Noto Sans TC", 11)
        )
        self.widgets['avmoney'].pack(side=LEFT)
        
        # 按鈕區域
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=X, pady=(15, 0))
        
        # 登入按鈕
        self.login_button = ttk.Button(
            button_frame,
            text="登入",
            bootstyle="success-outline",
            width=12,
            command=self._handle_login
        )
        self.login_button.pack(side=LEFT)
        
        # 停止按鈕
        self.stop_button = ttk.Button(
            button_frame,
            text="停止",
            bootstyle="danger-outline",
            width=12,
            command=self._handle_stop
        )
        self.stop_button.pack(side=RIGHT)
        
        # 初始化按鈕狀態
        self.update_button_states(False)
            
    def _handle_edit_click(self, event):
        """處理編輯按鈕點擊"""
        if not self.edit_enabled:
            return
            
        try:
            from gui.components.dialogs.add_device import AddDeviceDialog
            # 使用 winfo_toplevel() 獲取主視窗
            # 向上尋找 DevicesFrame 實例
            devices_frame = self.master
            while devices_frame and not hasattr(devices_frame, 'device_manager'):
                devices_frame = devices_frame.master
                
            if not devices_frame or not hasattr(devices_frame, 'device_manager'):
                raise Exception("找不到設備管理器")
                
            dialog = AddDeviceDialog(devices_frame, self.device_data)
            dialog.grab_set()  # 使對話框成為模態
        except Exception as e:
            log_manager.error(f"處理編輯按鈕點擊時發生錯誤: {str(e)}")
            
    def _handle_login(self):
        """處理登入按鈕點擊"""
        try:
            self.on_login(
                self.device_data["id"],
                self.device_data["bank_code"],
                self.device_data["bank_account"]
            )
        except Exception as e:
            log_manager.error(f"處理登入按鈕點擊時發生錯誤: {str(e)}")
            
    def _handle_stop(self):
        """處理停止按鈕點擊"""
        try:
            self.on_stop(self.device_data["id"])
        except Exception as e:
            log_manager.error(f"處理停止按鈕點擊時發生錯誤: {str(e)}")
            
    def update_button_states(self, is_running=False):
        """更新按鈕狀態"""
        # 登入按鈕只在設備啟用且未運行時可用
        self.login_button.configure(
            state="normal" if self.device_data["enabled"] and not is_running else "disabled"
        )
        
        # 更新編輯按鈕狀態
        self.edit_enabled = self.device_data["enabled"]
        self.edit_button.configure(
            bootstyle="info-inverse" if self.edit_enabled else "secondary-inverse",
            cursor="hand2" if self.edit_enabled else ""
        )
        
        # 停止按鈕只在運行時可用，不受設備啟用狀態影響
        self.stop_button.configure(
            state="normal" if is_running else "disabled"
        )
        
        # 如果設備未啟用，添加提示
        if not self.device_data["enabled"]:
            tooltip_text = "目前設備不可使用，請聯繫後台管理員"
            self.edit_button.bind('<Enter>', lambda e: self.show_tooltip(e, tooltip_text))
            self.edit_button.bind('<Leave>', self.hide_tooltip)
            self.login_button.bind('<Enter>', lambda e: self.show_tooltip(e, tooltip_text))
            self.login_button.bind('<Leave>', self.hide_tooltip)
        else:
            # 移除提示
            self.edit_button.unbind('<Enter>')
            self.edit_button.unbind('<Leave>')
            self.login_button.unbind('<Enter>')
            self.login_button.unbind('<Leave>')
            
    def show_tooltip(self, event, text):
        """顯示提示訊息"""
        try:
            self.hide_tooltip()  # 確保先清除任何現有的提示框
            
            x, y, _, _ = event.widget.bbox("insert")
            x += event.widget.winfo_rootx() + 25
            y += event.widget.winfo_rooty() + 20

            # 創建提示視窗
            self.tooltip = tk.Toplevel()
            self.tooltip.wm_overrideredirect(True)
            self.tooltip.wm_geometry(f"+{x}+{y}")

            label = ttk.Label(
                self.tooltip,
                text=text,
                justify=LEFT,
                background="#2C3E50",
                foreground="white",
                relief=SOLID,
                borderwidth=1,
                font=("Noto Sans TC", 10),
                padding=(5, 2)
            )
            label.pack(ipadx=1)
        except Exception as e:
            log_manager.error(f"顯示提示訊息時發生錯誤: {str(e)}")

    def hide_tooltip(self, event=None):
        """隱藏提示訊息"""
        try:
            if self.tooltip:
                self.tooltip.destroy()
                self.tooltip = None
        except Exception as e:
            log_manager.error(f"隱藏提示訊息時發生錯誤: {str(e)}")
            
    def update_device(self, new_device_data):
        """更新設備數據"""
        try:
            old_data = self.device_data
            self.device_data = new_device_data
            
            # 更新設備名稱
            if (old_data["device_name"] != new_device_data["device_name"] or
                old_data.get("account_type", 1) != new_device_data.get("account_type", 1)):
                account_type_text = "(個人)" if new_device_data.get("account_type", 1) == 1 else "(公司)"
                self.widgets['device_name'].configure(
                    text=f"{new_device_data['device_name']}{account_type_text}"
                )
            
            # 更新狀態
            if old_data["status"] != new_device_data["status"]:
                status_style = "success" if new_device_data["status"] == 1 else "danger"
                status_text = "在線" if new_device_data["status"] == 1 else "離線"
                self.widgets['status'].configure(
                    text=status_text,
                    bootstyle=f"{status_style}-inverse"
                )
            
            # 更新銀行信息
            bank_info = bank_registry.get_bank_by_code(new_device_data["bank_code"])
            bank_name = bank_info.name if bank_info else "未知銀行"
            if old_data["bank_code"] != new_device_data["bank_code"]:
                self.widgets['bank'].configure(text=f"銀行：{bank_name}")
            
            # 更新帳號
            if old_data["bank_account"] != new_device_data["bank_account"]:
                self.widgets['account'].configure(text=f"帳號：{new_device_data['bank_account']}")
            
            # 更新當月限額
            monthly_limit = float(new_device_data['monthly_limit'])
            monthly_remaining = float(new_device_data['remaining_monthly_limit'])
            self.widgets['monthly'].configure(
                text=f"當月剩餘/限額：{monthly_remaining:,.0f}/{monthly_limit:,.0f}"
            )
            
            # 更新當日限額
            daily_limit = float(new_device_data['daily_limit'])
            daily_remaining = float(new_device_data['remaining_daily_limit'])
            self.widgets['daily'].configure(
                text=f"當日剩餘/限額：{daily_remaining:,.0f}/{daily_limit:,.0f}"
            )
            
            # 更新結算金額
            avmoney = float(new_device_data.get('avmoney', '0.00'))
            self.widgets['avmoney'].configure(
                text=f"結算金額：{avmoney:,.2f}"
            )
            
            # 檢查是否可以啟用自動填入
            can_auto_fill = all(
                new_device_data.get(field) 
                for field in ['bank_id_number', 'bank_user_id', 'bank_user_password']
            )
            
            # 更新自動填入核取方塊狀態
            self.widgets['auto_fill'].configure(
                state="normal" if can_auto_fill else "disabled"
            )
            
            
            # 更新按鈕狀態
            is_running = new_device_data["status"] == 1
            self.update_button_states(is_running)

            # 更新代理設定顯示
            if old_data.get("use_proxy", False) != new_device_data.get("use_proxy", False):
                 self.use_proxy_var.set(new_device_data.get("use_proxy", False))

        except Exception as e:
            log_manager.error(f"更新設備卡片時發生錯誤: {str(e)}")
            
    def _handle_auto_fill_change(self):
        """處理自動填入設定變更"""
        try:
            import json
            import os
            
            settings_path = 'device_settings.json'
            
            # 讀取現有設定
            settings = {"devices": {}, "global": {}}
            if os.path.exists(settings_path):
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                except json.JSONDecodeError:
                    log_manager.error("設定檔案格式錯誤，將重新建立")
            
            # 更新設定
            device_id = str(self.device_data["id"])  # 確保 ID 是字串
            if device_id not in settings["devices"]:
                settings["devices"][device_id] = {}
            settings["devices"][device_id]["auto_fill"] = self.auto_fill_var.get()
            
            # 保存設定
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
                
            log_manager.debug(f"已更新設備 {device_id} 的自動填入設定為: {self.auto_fill_var.get()}")
                
        except Exception as e:
            log_manager.error(f"更新自動填入設定時發生錯誤: {str(e)}")

    def _handle_proxy_change(self):
        """處理代理設定變更"""
        try:
            import json
            import os

            settings_path = 'device_settings.json'

            # 讀取現有設定
            settings = {"devices": {}, "global": {}}
            if os.path.exists(settings_path):
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                except json.JSONDecodeError:
                    log_manager.error("設定檔案格式錯誤，將重新建立")

            # 更新設定
            device_id = str(self.device_data["id"])
            if device_id not in settings["devices"]:
                 settings["devices"][device_id] = {} # 如果設備不存在，先創建字典
            settings["devices"][device_id]["use_proxy"] = self.use_proxy_var.get()

            # 保存設定
            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            log_manager.debug(f"已更新設備 {device_id} 的代理設定為: {self.use_proxy_var.get()}")

            # 同步更新 device_data 中的值，以便下次編輯時能正確顯示
            self.device_data["use_proxy"] = self.use_proxy_var.get()

        except Exception as e:
            log_manager.error(f"更新代理設定時發生錯誤: {str(e)}")

    def load_settings(self):
        """載入設備相關設定 (自動填入、代理等)"""
        try:
            import json
            import os

            settings_path = 'device_settings.json'

            if os.path.exists(settings_path):
                with open(settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                device_id = str(self.device_data["id"])
                device_settings = settings.get("devices", {}).get(device_id, {})

                # 載入自動填入設定
                can_auto_fill = all(
                    self.device_data.get(field)
                    for field in ['bank_id_number', 'bank_user_id', 'bank_user_password']
                )
                if can_auto_fill:
                    self.auto_fill_var.set(device_settings.get("auto_fill", False))
                    log_manager.debug(f"已載入設備 {device_id} 的自動填入設定: {device_settings.get('auto_fill', False)}")
                else:
                    self.auto_fill_var.set(False) # 如果資料不全，強制關閉
                    self.widgets['auto_fill'].configure(state="disabled")

                # 載入代理設定
                self.use_proxy_var.set(device_settings.get("use_proxy", False))
                log_manager.debug(f"已載入設備 {device_id} 的代理設定: {device_settings.get('use_proxy', False)}")

        except Exception as e:
            log_manager.error(f"載入設備設定時發生錯誤: {str(e)}")
            
    def destroy(self):
        """銷毀卡片"""
        try:
            # 清理提示框
            self.hide_tooltip()
            
            # 解除事件綁定
            self.edit_button.unbind('<Enter>')
            self.edit_button.unbind('<Leave>')
            self.login_button.unbind('<Enter>')
            self.login_button.unbind('<Leave>')
            
            # 清理所有存儲的小部件
            for widget in self.widgets.values():
                widget.destroy()
            self.widgets.clear()
            
            # 調用父類的銷毀方法
            super().destroy()
            
        except Exception as e:
            log_manager.error(f"銷毀設備卡片時發生錯誤: {str(e)}")
