import os
import time
import asyncio
import sys # 需要 sys 來檢查打包狀態
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON>rror as PlaywrightError
from dotenv import load_dotenv
from .proxy_utils import generate_proxy_config
from .logger import log_manager
from .playwright_utils import get_playwright_browsers_path # 導入新的工具函數
import heapq

# 加載 .env 文件

async def test_single_proxy(proxy_config, test_url, timeout_ms, executable_path=None):
    """
    測試單個代理的連接速度。

    Args:
        proxy_config (dict): 代理配置字典 (包含 server, username, password)。
        test_url (str): 測試目標 URL。
        timeout_ms (int): Playwright 操作的超時時間 (毫秒)。
        executable_path (str, optional): 瀏覽器執行檔的完整路徑。

    Returns:
        tuple: (響應時間(秒), 代理配置字典) 或 (float('inf'), None) 如果失敗。
    """
    start_time = time.monotonic()
    browser = None
    context = None
    page = None
    playwright_instance = None

    try:
        # --- 設置 Playwright 瀏覽器路徑環境變數 (主要用於打包環境) ---
        # 注意：這應該在 async_playwright().start() 之前設置
        # 但由於 test_single_proxy 是異步函數且可能並行運行，
        # 在這裡設置可能不是最理想的，最好在 run_proxy_test_async 開始時設置一次。
        # 暫時保留在此處，但標記為潛在改進點。
        # browser_path_for_env = get_playwright_browsers_path()
        # if browser_path_for_env:
        #     os.environ['PLAYWRIGHT_BROWSERS_PATH'] = browser_path_for_env
        # --- 設置結束 ---

        playwright_instance = await async_playwright().start()
        # 嘗試傳遞 executable_path (如果有的話)，但優先依賴環境變數
        launch_options = {
             'headless': True, # 使用無頭模式
        }
        if executable_path:
             launch_options['executable_path'] = executable_path
             log_manager.debug(f"在 test_single_proxy 中使用 executable_path: {executable_path}")

        browser = await playwright_instance.chromium.launch(
             **launch_options
        )
        context = await browser.new_context(
            proxy=proxy_config,
            ignore_https_errors=True # 忽略 HTTPS 錯誤以提高成功率
        )
        page = await context.new_page()

        # 導航到測試 URL
        await page.goto(test_url, timeout=timeout_ms, wait_until='load') # 等待頁面加載完成

        # 簡單檢查頁面內容，確保不是錯誤頁面 (可選)
        content = await page.content()
        if "error" in content.lower() or "fail" in content.lower():
             log_manager.debug(f"代理 {proxy_config['server']} 連接成功但頁面內容可能表示錯誤。")
             # 根據需要決定是否視為失敗
             # return float('inf'), None

        end_time = time.monotonic()
        response_time = end_time - start_time
        log_manager.debug(f"代理 {proxy_config['server']} (User: {proxy_config['username']}) 測試成功，耗時: {response_time:.4f} 秒")
        # 返回 response_time 和完整的 proxy_config
        return response_time, proxy_config

    except PlaywrightTimeoutError:
        log_manager.debug(f"代理 {proxy_config['server']} 測試超時 ({timeout_ms / 1000} 秒)")
        return float('inf'), None
    except PlaywrightError as e:
        # 捕捉更廣泛的 Playwright 錯誤
        log_manager.debug(f"代理 {proxy_config['server']} 測試時發生 Playwright 錯誤: {e}")
        return float('inf'), None
    except Exception as e:
        log_manager.warning(f"代理 {proxy_config['server']} 測試時發生未預期錯誤: {e}")
        return float('inf'), None
    finally:
        # 確保資源被釋放
        if page:
            try: await page.close()
            except Exception: pass
        if context:
            try: await context.close()
            except Exception: pass
        if browser:
            try: await browser.close()
            except Exception: pass
        if playwright_instance:
            try: await playwright_instance.stop()
            except Exception: pass


async def run_proxy_test_async(settings):
    """
    異步執行代理速度測試。

    Args:
        settings (dict): 包含代理測速設定的字典。
    """
    # --- 獲取 Playwright 瀏覽器路徑和執行檔路徑 ---
    ms_playwright_root, chromium_executable_path, headless_executable_path = get_playwright_browsers_path()

    if ms_playwright_root:
        os.environ['PLAYWRIGHT_BROWSERS_PATH'] = ms_playwright_root
        log_manager.debug(f"已設定 PLAYWRIGHT_BROWSERS_PATH 環境變數為: {os.environ.get('PLAYWRIGHT_BROWSERS_PATH')}")
    else:
         log_manager.warning("未能獲取 Playwright 瀏覽器根目錄，可能導致啟動失敗。")

    if chromium_executable_path:
         log_manager.debug(f"獲取到 Chromium 瀏覽器執行檔路徑: {chromium_executable_path}")
    else:
         log_manager.warning("未能獲取 Chromium 瀏覽器執行檔路徑。")

    if headless_executable_path:
         log_manager.debug(f"獲取到 Chromium Headless Shell 執行檔路徑: {headless_executable_path}")
    else:
         log_manager.warning("未能獲取 Chromium Headless Shell 執行檔路徑。")
    # --- 獲取結束 ---


    # 從 settings 字典中獲取參數
    proxy_test_timeout_seconds = settings.get("PROXY_TEST_TIMEOUT_SECONDS", 1.0)
    proxy_test_keep_count = settings.get("PROXY_TEST_KEEP_COUNT", 10)
    proxy_test_batch_size = settings.get("PROXY_TEST_BATCH_SIZE", 5)
    max_test_proxies = settings.get("MAX_TEST_PROXIES", 100)
    # 這些參數仍然從環境變數讀取，因為它們與測速邏輯本身無關，而是與代理生成和輸出文件相關
    proxy_test_url = os.getenv('PROXY_TEST_URL', 'https://api.ipify.org')
    fast_proxy_json_file = os.getenv('FAST_PROXY_JSON_FILE', 'proxy.json')

    log_manager.info("開始異步代理測速...")
    log_manager.info(f"測試 URL: {proxy_test_url}")
    log_manager.info(f"超時設定: {proxy_test_timeout_seconds} 秒")
    log_manager.info(f"批次大小: {proxy_test_batch_size}")
    log_manager.info(f"保留數量: {proxy_test_keep_count}")
    log_manager.info(f"最多測試: {max_test_proxies}")
    log_manager.info(f"快速代理輸出 JSON 文件: {fast_proxy_json_file}")

    # 使用最小堆來高效地維護最快的 N 個代理
    # 存儲 (-time, count, proxy_config)，count 作為次要排序鍵避免字典比較
    fast_proxies_heap = []
    entry_count = 0 # 新增：唯一計數器
    tested_count = 0
    successful_tests = 0

    # Playwright 的超時是以毫秒為單位
    playwright_timeout_ms = int(proxy_test_timeout_seconds * 1000)

    while tested_count < max_test_proxies and len(fast_proxies_heap) < proxy_test_keep_count:
        tasks = []
        batch_proxies = []
        for _ in range(proxy_test_batch_size):
            proxy_config = generate_proxy_config()
            if proxy_config:
                batch_proxies.append(proxy_config)
                # 將 headless_executable_path 傳遞給 test_single_proxy
                tasks.append(test_single_proxy(proxy_config, proxy_test_url, playwright_timeout_ms, executable_path=headless_executable_path))

        if not tasks:
            log_manager.warning("無法生成任何代理進行測試，停止。")
            break

        results = await asyncio.gather(*tasks)
        tested_count += len(results)

        # results 現在包含 (response_time, proxy_config) 或 (inf, None)
        for response_time, proxy_config_result in results:
            # 確保 proxy_config_result 不是 None
            if proxy_config_result and response_time <= proxy_test_timeout_seconds:
                successful_tests += 1
                entry = (-response_time, entry_count, proxy_config_result)
                entry_count += 1 # 遞增計數器
                # 如果堆未滿，直接加入
                if len(fast_proxies_heap) < proxy_test_keep_count:
                    heapq.heappush(fast_proxies_heap, entry)
                # 如果新代理比堆中最慢的代理快，則替換
                # heapq 會先比較第一個元素 (-response_time)，如果相同則比較第二個 (entry_count)
                elif entry < fast_proxies_heap[0]: # 直接比較元組
                    heapq.heapreplace(fast_proxies_heap, entry)

        log_manager.info(f"已測試 {tested_count}/{max_test_proxies} 個代理。目前找到 {len(fast_proxies_heap)}/{proxy_test_keep_count} 個快速代理。成功率: {successful_tests}/{tested_count} ({(successful_tests/tested_count*100):.1f}%)")

        # 如果已找到足夠數量的快速代理，可以提前停止（可選）
        # if len(fast_proxies_heap) >= proxy_test_keep_count:
        #     log_manager.info(f"已找到 {proxy_test_keep_count} 個快速代理，提前結束測試。")
        #     break

    # 從堆中提取結果並排序（按時間升序）
    # fast_proxies_data 現在包含 (time, count, proxy_config)
    # 我們只需要 time 和 config 來寫入 JSON
    fast_proxies_data = sorted([( -neg_time, config) for neg_time, _, config in fast_proxies_heap], key=lambda x: x[0])

    # 準備要寫入 JSON 的數據結構
    output_data = []
    for response_time, config in fast_proxies_data: # 迭代排序後的列表
        output_data.append({
            "proxy_config": config,
            "response_time_seconds": round(response_time, 4), # 保留4位小數
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) # 記錄測試時間
        })

    # 寫入 JSON 文件
    import json # 導入 json 模塊
    try:
        with open(fast_proxy_json_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=4, ensure_ascii=False) # 寫入 JSON，美化格式
        log_manager.info(f"已將 {len(output_data)} 個快速代理詳細資訊寫入到 {fast_proxy_json_file}")
        return len(output_data)
    except Exception as e:
        log_manager.error(f"寫入快速代理 JSON 文件 {fast_proxy_json_file} 時發生錯誤: {e}")
        return None


def run_proxy_test(settings):
    """
    同步包裝器，用於從非異步代碼（如 Tkinter 回調）調用。

    Args:
        settings (dict): 包含代理測速設定的字典。
    """
    try:
        # 運行異步函數，並傳遞 settings
        result = asyncio.run(run_proxy_test_async(settings))
        return result
    except RuntimeError as e:
        # 處理 asyncio.run 在某些情況下（如線程中已有事件循環）可能引發的錯誤
        if "cannot run event loop while another loop is running" in str(e):
            log_manager.warning("檢測到現有事件循環，嘗試使用 nest_asyncio 或在新線程中創建新循環。")
            # 這裡可以添加更複雜的處理，例如使用 nest_asyncio 或確保在乾淨的線程中運行
            # 為了簡單起見，暫時返回錯誤
            log_manager.error("無法在現有事件循環中運行測速。")
            return None
        else:
            log_manager.error(f"運行代理測速時發生 RuntimeError: {e}")
            return None
    except Exception as e:
        log_manager.error(f"運行代理測速時發生未預期錯誤: {e}")
        return None

if __name__ == '__main__':
    # 允許直接運行此腳本進行測試
    print("正在直接運行代理測速腳本...")
    # 提供一個預設的 settings 字典用於直接運行測試
    default_settings_for_cli = {
        "PROXY_TEST_TIMEOUT_SECONDS": 3.0,
        "PROXY_TEST_KEEP_COUNT": 20,
        "PROXY_TEST_BATCH_SIZE": 5,
        "MAX_TEST_PROXIES": 500
    }
    found_count = run_proxy_test(default_settings_for_cli)
    if found_count is not None:
        print(f"測試完成，找到 {found_count} 個快速代理。")
    else:
        print("測試過程中發生錯誤。")