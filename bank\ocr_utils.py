"""
驗證碼 OCR 識別工具
提供簡潔的驗證碼識別介面
"""

import time
from .ocr_manager import ocr_manager
from gui.utils.logger import log_manager

class CaptchaOCR:
    """驗證碼 OCR 識別器"""

    def __init__(self):
        self.manager = ocr_manager

    def is_available(self):
        """檢查 OCR 功能是否可用"""
        return self.manager.is_available

    def ensure_ready(self, progress_callback=None, completion_callback=None):
        """
        確保 OCR 功能就緒
        如果未安裝則自動安裝

        Args:
            progress_callback: 進度回調函數 callback(progress, message)
            completion_callback: 完成回調函數 callback(success, message)

        Returns:
            bool: 如果已就緒返回 True，如果需要安裝返回 False
        """
        if self.manager.check_installation():
            return True

        # 需要安裝
        log_manager.info("OCR 功能未就緒，開始自動安裝...")
        if progress_callback or completion_callback:
            # 異步安裝
            self.manager.install_async(progress_callback, completion_callback)
            return False
        else:
            # 同步安裝
            return self.manager.install_ddddocr()

    def recognize_from_element(self, element_locator, device_id=None, max_retries=2):
        """
        從頁面元素識別驗證碼

        Args:
            element_locator: Playwright 元素定位器
            device_id: 設備 ID（用於日誌）
            max_retries: 最大重試次數

        Returns:
            str: 識別結果，失敗時返回 None
        """
        if not self.is_available():
            log_manager.warning("OCR 功能不可用，請先安裝", device=device_id)
            return None

        for attempt in range(max_retries + 1):
            try:
                # 等待元素可見
                element_locator.wait_for(state="visible", timeout=10000)

                # 截取驗證碼圖片
                screenshot_bytes = element_locator.screenshot()

                # 使用 OCR 識別
                result = self.manager.recognize_captcha(screenshot_bytes, device_id)

                if result and len(result) >= 3:  # 驗證結果合理性
                    return result

                if attempt < max_retries:
                    log_manager.info(f"第 {attempt + 1} 次識別結果不理想，重試中...", device=device_id)
                    time.sleep(0.5)

            except Exception as e:
                log_manager.error(f"第 {attempt + 1} 次識別時發生錯誤: {str(e)}", device=device_id)
                if attempt < max_retries:
                    time.sleep(0.5)

        log_manager.warning(f"驗證碼識別失敗，已重試 {max_retries} 次", device=device_id)
        return None

    def recognize_from_url(self, page, image_url, device_id=None, max_retries=2):
        """
        從圖片 URL 識別驗證碼

        Args:
            page: Playwright 頁面對象
            image_url: 驗證碼圖片 URL
            device_id: 設備 ID（用於日誌）
            max_retries: 最大重試次數

        Returns:
            str: 識別結果，失敗時返回 None
        """
        if not self.is_available():
            log_manager.warning("OCR 功能不可用，請先安裝", device=device_id)
            return None

        for attempt in range(max_retries + 1):
            try:
                # 使用頁面上下文獲取圖片
                response = page.request.get(image_url)
                if response.status != 200:
                    log_manager.error(f"獲取驗證碼圖片失敗，狀態碼: {response.status}", device=device_id)
                    return None

                image_bytes = response.body()

                # 使用 OCR 識別
                result = self.manager.recognize_captcha(image_bytes, device_id)

                if result and len(result) >= 3:  # 驗證結果合理性
                    return result

                if attempt < max_retries:
                    log_manager.info(f"第 {attempt + 1} 次識別結果不理想，重試中...", device=device_id)
                    time.sleep(0.5)

            except Exception as e:
                log_manager.error(f"第 {attempt + 1} 次從 URL 識別時發生錯誤: {str(e)}", device=device_id)
                if attempt < max_retries:
                    time.sleep(0.5)

        log_manager.warning(f"從 URL 識別驗證碼失敗，已重試 {max_retries} 次", device=device_id)
        return None

    def recognize_with_fallback(self, element_locator, device_id=None, fallback_callback=None):
        """
        帶降級處理的驗證碼識別
        OCR 失敗時可以回調到手動輸入

        Args:
            element_locator: Playwright 元素定位器
            device_id: 設備 ID
            fallback_callback: 降級回調函數，應返回手動輸入的驗證碼

        Returns:
            str: 識別結果或手動輸入結果
        """
        # 首先嘗試 OCR 識別
        result = self.recognize_from_element(element_locator, device_id)

        if result:
            return result

        # OCR 失敗，使用降級處理
        log_manager.info("OCR 識別失敗，切換到手動輸入模式", device=device_id)

        if fallback_callback:
            try:
                manual_result = fallback_callback()
                if manual_result:
                    log_manager.info("使用手動輸入的驗證碼", device=device_id)
                    return manual_result
            except Exception as e:
                log_manager.error(f"手動輸入回調失敗: {e}", device=device_id)

        return None

# 全局 OCR 實例
captcha_ocr = CaptchaOCR()
