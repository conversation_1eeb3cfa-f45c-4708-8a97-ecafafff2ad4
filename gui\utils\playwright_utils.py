import os
import sys
import subprocess
import re
from pathlib import Path
from .logger import log_manager # 假設 logger 在同一層級

def find_node_path():
    """
    嘗試尋找 Node.js 的安裝路徑。
    """
    try:
        # 嘗試執行 where node (Windows) 或 which node (Linux/macOS)
        command = 'where node' if sys.platform == 'win32' else 'which node'
        log_manager.debug(f"嘗試執行 '{command}' 尋找 Node.js 路徑...")
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True,
            shell=True if sys.platform == 'win32' else False, # 在 Windows 上使用 shell=True 可能有助於找到命令
            encoding='utf-8'
        )
        node_executable_path = result.stdout.strip()
        log_manager.debug(f"找到 Node.js 執行檔路徑: {node_executable_path}")
        # 返回 Node.js 執行檔所在的目錄
        return os.path.dirname(node_executable_path)
    except FileNotFoundError:
        log_manager.error("找不到 'node' 或相關指令，請確認 Node.js 已安裝並在系統 PATH 中。")
        return None
    except subprocess.CalledProcessError as e:
        log_manager.error(f"執行尋找 Node.js 路徑指令時發生錯誤: {e}")
        log_manager.error(f"標準輸出:\n{e.stdout}")
        log_manager.error(f"標準錯誤:\n{e.stderr}")
        return None
    except Exception as e:
        log_manager.error(f"尋找 Node.js 路徑時發生未知錯誤: {e}")
        return None

def get_playwright_browsers_path():
    """
    獲取 PLAYWRIGHT_BROWSERS_PATH 環境變數應設置的路徑，
    以及最新版本 Chromium 和 Chromium Headless Shell 執行檔的完整路徑。
    通過執行 'npx playwright install --dry-run' 並解析輸出獲取。
    在執行前嘗試將 Node.js 路徑添加到 PATH。

    Returns:
        tuple: (ms-playwright 目錄路徑, Chromium 執行檔完整路徑, Chromium Headless Shell 執行檔完整路徑)
               如果找不到路徑或執行指令失敗，則返回 (None, None, None)。
    """
    # 嘗試找到 Node.js 路徑並添加到當前進程的 PATH
    node_path = find_node_path()
    original_path = os.environ.get('PATH', '')
    if node_path and node_path not in original_path:
        # 將 Node.js 路徑添加到 PATH 的最前面
        os.environ['PATH'] = f"{node_path}{os.pathsep}{original_path}"
        log_manager.debug(f"已將 Node.js 路徑 '{node_path}' 添加到當前進程的 PATH 中。")
    elif node_path:
         log_manager.debug(f"Node.js 路徑 '{node_path}' 已在 PATH 中。")
    else:
         log_manager.warning("未能找到 Node.js 路徑，可能無法執行 npx 指令。")

    ms_playwright_root = None
    latest_chromium_executable_path = None
    latest_headless_executable_path = None

    try:
        # 執行指令並捕獲輸出
        log_manager.debug("執行 'npx playwright install --dry-run' 獲取瀏覽器路徑...")
        result = subprocess.run(
            ['npx', 'playwright', 'install', '--dry-run'],
            capture_output=True,
            text=True,
            check=True,
            shell=True if sys.platform == 'win32' else False, # 根據平台設定 shell
            encoding='utf-8' # 指定編碼
        )
        output = result.stdout
        log_manager.debug(f"指令輸出:\n{output}")

        # 解析輸出，尋找所有瀏覽器的安裝路徑和執行檔路徑
        install_locations = {}
        install_location_prefix = "Install location:"
        for line in output.splitlines():
            if install_location_prefix in line:
                parts = line.split(install_location_prefix, 1)
                if len(parts) > 1:
                    path = parts[1].strip()
                    # 從路徑中提取瀏覽器名稱和版本
                    match = re.search(r'\\(chromium|chromium_headless_shell)-(\d+)', path)
                    if match:
                        browser_name = match.group(1)
                        version = int(match.group(2))
                        # 儲存最新版本的路徑
                        if browser_name not in install_locations or version > install_locations[browser_name]['version']:
                            install_locations[browser_name] = {'version': version, 'path': path}

        # 尋找最新版本的 Chromium 或 Chromium Headless Shell 目錄
        latest_chromium_dir = install_locations.get('chromium', {}).get('path')
        latest_headless_dir = install_locations.get('chromium_headless_shell', {}).get('path')

        if latest_chromium_dir:
            log_manager.debug(f"找到最新版本 Chromium 目錄: {latest_chromium_dir}")
            # 提取 ms-playwright 根目錄
            ms_playwright_root = str(Path(latest_chromium_dir).parent)
            log_manager.debug(f"解析到 Playwright 瀏覽器根目錄: {ms_playwright_root}")

            # 構建 Chromium 執行檔的完整路徑
            executable_name = 'chrome.exe' if sys.platform == 'win32' else 'chrome'
            executable_path_candidate = os.path.join(latest_chromium_dir, 'chrome-win', executable_name)
            if os.path.exists(executable_path_candidate):
                latest_chromium_executable_path = executable_path_candidate
                log_manager.debug(f"找到最新版本 Chromium 執行檔: {latest_chromium_executable_path}")
            else:
                 log_manager.warning(f"在 {latest_chromium_dir} 中找不到 Chromium 執行檔 {executable_name}。")

        if latest_headless_dir:
            log_manager.debug(f"找到最新版本 Chromium Headless Shell 目錄: {latest_headless_dir}")
            # 如果 ms_playwright_root 還沒有找到，從 headless 路徑提取
            if not ms_playwright_root:
                 ms_playwright_root = str(Path(latest_headless_dir).parent)
                 log_manager.debug(f"解析到 Playwright 瀏覽器根目錄 (從 headless 路徑): {ms_playwright_root}")

            # 構建 Chromium Headless Shell 執行檔的完整路徑
            headless_executable_name = 'headless_shell.exe' if sys.platform == 'win32' else 'headless_shell'
            headless_executable_path_candidate = os.path.join(latest_headless_dir, 'chrome-win', headless_executable_name)
            if os.path.exists(headless_executable_path_candidate):
                latest_headless_executable_path = headless_executable_path_candidate
                log_manager.debug(f"找到最新版本 Chromium Headless Shell 執行檔: {latest_headless_executable_path}")
            else:
                 log_manager.warning(f"在 {latest_headless_dir} 中找不到 Chromium Headless Shell 執行檔 {headless_executable_name}。")


    except FileNotFoundError:
        log_manager.error("找不到 'npx' 或 'playwright' 指令，請確認 Node.js 和 Playwright 已正確安裝。")
        return None, None, None
    except subprocess.CalledProcessError as e:
        log_manager.error(f"執行 'npx playwright install --dry-run' 時發生錯誤: {e}")
        log_manager.error(f"標準輸出:\n{e.stdout}")
        log_manager.error(f"標準錯誤:\n{e.stderr}")
        return None, None, None
    except Exception as e:
        log_manager.error(f"獲取 Playwright 瀏覽器路徑時發生未知錯誤: {e}")
        return None, None, None
    finally:
        # 恢復原始 PATH 環境變數 (如果修改過)
        if node_path and node_path not in original_path:
             os.environ['PATH'] = original_path
             log_manager.debug("已恢復原始 PATH 環境變數。")

    return ms_playwright_root, latest_chromium_executable_path, latest_headless_executable_path