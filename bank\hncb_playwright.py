"""
華南銀行操作模組 (Playwright 版本)
實作華南銀行特定的自動化操作
"""

import time
import json
from playwright.sync_api import TimeoutError, expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class HuaNanBank(BankBase):
    """華南銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        super().__init__(
            bank_code='008',  # 華南銀行代碼
            bank_account=str(bank_account),  # 確保帳號是字串
            device_id=device_id,
            device_data=device_data,
            is_mobile=False,  # 使用電腦版網頁
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = "https://netbank.hncb.com.tw/netbank/servlet/TrxDispatcher?trx=com.lb.wibc.trx.Login&state=prompt&Recognition=private"
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        self.keep_alive_interval = 300  # 保持登入間隔（秒）
        self.last_keep_alive = 0  # 上次保持登入的時間
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 等待左側選單 frame
            left_frame = self.page.frame_locator("frame[name=\"left\"]").first
            if not left_frame:
                return False
                
            # 檢查帳務查詢連結是否存在
            account_query_link = left_frame.get_by_role("link", name="帳務查詢")
            if account_query_link.is_visible():
                log_manager.debug("登入狀態確認成功", device=self.device_id)
                return True
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
            return False
            
    def verify_account(self):
        """驗證帳號"""
        if not self.is_running:
            return False
            
        try:
            # 點擊帳務查詢
            left_frame = self.page.frame_locator("frame[name=\"left\"]").first
            account_query_link = left_frame.get_by_role("link", name="帳務查詢")
            account_query_link.click()
            
            # 等待主要內容載入
            self.page.wait_for_load_state("networkidle")
            time.sleep(2)  # 給予一些時間讓 frame 完全載入
            
            # --- 帳號驗證重試邏輯 ---
            max_retries = 3
            retry_delay = 5
            account_found = False

            for attempt in range(max_retries):
                if not self.is_running: return False # 每次重試前檢查

                log_manager.debug(f"開始驗證帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                try:
                    # 獲取 main frame
                    main_frame = self.page.frame("main")
                    if not main_frame:
                        # 嘗試備用方法獲取 frame
                        main_frame = next((f for f in self.page.frames if f.name == "main"), None)

                    if not main_frame:
                        log_manager.warning(f"第 {attempt + 1} 次嘗試找不到主要內容 frame (main)", device=self.device_id)
                        # Frame 找不到，可能需要等待或頁面結構改變，進入重試
                    else:
                        # 檢查每一列是否包含目標帳號
                        account_rows = main_frame.query_selector_all("form table tr.Table_contentWt_C, form table tr.Table_contentBu_C")
                        account_found_in_this_attempt = False
                        for row in account_rows:
                            account_text = row.text_content()
                            if self.bank_account in account_text:
                                log_manager.info(f"帳號驗證成功: {self.bank_account} (嘗試 {attempt + 1})", device=self.device_id)
                                account_found = True
                                account_found_in_this_attempt = True
                                break # 找到帳號，跳出內層循環

                        if account_found:
                            break # 找到帳號，跳出外層重試循環

                        if not account_found_in_this_attempt:
                             log_manager.warning(f"第 {attempt + 1} 次嘗試未在表格中找到帳號: {self.bank_account}", device=self.device_id)

                    # --- 重試處理 ---
                    if not account_found and attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    elif not account_found and attempt == max_retries - 1:
                        # 最後一次嘗試失敗
                        self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍未找到帳號 {self.bank_account}", "account_verify_retries_failed")
                        self.stop() # 驗證失敗，停止
                        return False

                except Exception as e:
                    log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(e)}", device=self.device_id)
                    # 發生意外錯誤，處理重試或停止邏輯
                    if attempt < max_retries - 1:
                        log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    else:
                        self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(e)}", "account_verify_error_final")
                        self.stop() # 最後一次嘗試也出錯，則停止
                        return False

            # --- 循環結束後 ---
            if account_found:
                return True
            else:
                # 理論上如果失敗會在循環內 return False，但為了完整性加上
                log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
                if self.is_running: self.stop() # 確保停止
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"驗證帳號時發生錯誤: {str(e)}",
                    "account_verify_error")
                self.stop()
            return False
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies"""
        if not self.is_running:
            return
            
        try:
            current_time = time.time()
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update < self.cookie_update_interval:
                return
                
            # 獲取所需的 cookies
            cookies = self.context.cookies()
            cookies_dict = {}
            
            # 提取需要的 cookies
            for cookie in cookies:
                if cookie["name"] in ["JSESSIONID", "NBSessionID", "v1st", "ASP.NET_SessionId"]:
                    cookies_dict[cookie["name"]] = cookie["value"]
                    
            if cookies_dict:
                if self.update_cookies(cookies_dict):
                    self.cookies_updated = True
                    self.last_cookie_update = current_time
                    self.update_status("已更新 cookies")
                else:
                    self.log_with_screenshot('warning',
                        "更新 cookies 失敗",
                        "update_cookies_failed")
                        
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"更新 cookies 時發生錯誤: {str(e)}",
                    "update_cookies_error")
                    
    def keep_alive(self):
        """保持登入狀態"""
        if not self.is_running:
            return False
            
        try:
            current_time = time.time()
            if current_time - self.last_keep_alive < self.keep_alive_interval:
                return True
                
            # 點擊帳務查詢保持登入
            left_frame = self.page.frame_locator("frame[name=\"left\"]").first
            account_query_link = left_frame.get_by_role("link", name="帳務查詢")
            account_query_link.click()
            
            self.last_keep_alive = current_time
            log_manager.debug("已執行保持登入操作", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"保持登入操作時發生錯誤: {str(e)}",
                    "keep_alive_error")
            return False
            
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            # 直接填入資料，不等待其他資源載入
            self.page.get_by_placeholder("請輸入身分證字號").fill(self.device_data["bank_id_number"])
            self.page.get_by_placeholder("請輸入您的代號").fill(self.device_data["bank_user_id"])
            self.page.get_by_placeholder("請輸入8~16位密碼").fill(self.device_data["bank_user_password"])

            # 嘗試識別並填入驗證碼 - 使用統一接口
            try:
                # 華南銀行專用選擇器 - 基於實際頁面結構
                captcha_selectors = [
                    'img[id="code_Cap"]',  # 華南銀行專用 - 精確 ID 選擇器
                    'img[name="code_Cap"]',  # 華南銀行專用 - 精確 name 選擇器
                    'img[src*="CaptchaImage"]',  # 華南銀行專用 - JSP 驗證碼頁面
                    'img[src*="captcha"]',  # 通用驗證碼圖片
                    'img[src*="verify"]',
                    'img[src*="code"]',
                    'img[src*="data:image"]',  # base64 圖片
                    'img[alt*="驗證"]',
                    'img[title*="驗證"]',
                    'img'  # 通用圖片選擇器（最後備用）
                ]
                input_selectors = [
                    'textbox[placeholder="圖形驗證碼"]',  # 華南銀行專用 - role-based 選擇器
                    'input[placeholder*="驗證"]',
                    'input[name*="captcha"]',
                    'input[name*="verify"]',
                    'input[id*="captcha"]',
                    'input[id*="verify"]'
                ]

                if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                    log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                    self.update_status("已自動填入(含驗證碼)")
                else:
                    log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                    self.update_status("已自動填入(需手動驗證碼)")
            except Exception as captcha_e:
                log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

            log_manager.debug("已自動填入登入表單", device=self.device_id)
            return True
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入表單時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟華南銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 訪問登入頁面，使用 networkidle 事件
                self.page.goto(self.login_url, wait_until="networkidle")
                
                # 立即嘗試自動填入，不等待其他資源載入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                        
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                # 驗證帳號
                self.update_status("登入成功，驗證帳號...")
                if not self.verify_account():
                    break  # verify_account 會自動呼叫 stop()
                    
                self.update_status("帳號驗證成功，開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    try:
                        # 更新 cookies
                        self.get_and_update_cookies()
                        
                        # 保持登入
                        self.keep_alive()
                        
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"監控過程中發生錯誤: {str(e)}",
                                "monitor_error")
                            time.sleep(5)
                            
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
                
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
            
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止華南銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器