"""
台新銀行操作模組 (Playwright 版本)
實作台新銀行特定的自動化操作
"""

import time
import re
from playwright.sync_api import expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class TaixinBank(BankBase):
    """台新銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=True, use_proxy: bool = False): # 新增 is_mobile 和 use_proxy 參數
        super().__init__(
            bank_code="812",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=is_mobile, # 傳遞手機版設定
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = "https://my.taishinbank.com.tw/TIBNetBank/"  # 登入網址
        self.expected_account = bank_account  # 保存初始帳號用於驗證
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 檢查臺幣總覽標題
            heading = self.page.locator("#main").content_frame.get_by_role("heading", name="臺幣總覽")
            return heading.is_visible()
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
                self.stop()  # 發生錯誤時停止執行緒
            return False
            
    def verify_account(self):
        """驗證帳號"""
        if not self.is_running:
            return False
            
        try:
            # --- 帳號驗證重試邏輯 ---
            max_retries = 3
            retry_delay = 5
            account_verified = False
            main_frame = self.page.locator("#main").content_frame # 先獲取 frame

            for attempt in range(max_retries):
                if not self.is_running: return False # 每次重試前檢查

                log_manager.debug(f"開始驗證台新帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
                try:
                    # 使用 xpath 定位所有帳號欄位
                    account_cells_locator = main_frame.locator('td[data-title="帳號"] ._table_tbody__col-content--content')
                    # 等待至少一個元素出現
                    account_cells_locator.first.wait_for(state="visible", timeout=10000)
                    account_cells = account_cells_locator.all()

                    if not account_cells:
                        log_manager.warning(f"第 {attempt + 1} 次嘗試找不到任何帳號欄位", device=self.device_id)
                        # 找不到欄位，進入重試邏輯
                    else:
                        # 從預期帳號中提取數字
                        expected_numbers = re.sub(r'[^0-9]', '', self.expected_account)
                        account_found_in_this_attempt = False

                        # 檢查所有帳號，只要有一個匹配就算成功
                        for account_cell in account_cells:
                            account_text = account_cell.inner_text()
                            account_numbers = re.sub(r'[^0-9]', '', account_text)

                            if account_numbers == expected_numbers:
                                log_manager.info(f"帳號驗證成功: {account_numbers} (嘗試 {attempt + 1})", device=self.device_id)
                                account_verified = True
                                account_found_in_this_attempt = True
                                break # 找到帳號，跳出內層循環

                        if account_verified:
                            break # 找到帳號，跳出外層重試循環

                        # 如果本次嘗試未找到匹配帳號
                        if not account_found_in_this_attempt:
                             all_accounts_text = [re.sub(r'[^0-9]', '', cell.inner_text()) for cell in account_cells]
                             log_manager.warning(f"第 {attempt + 1} 次嘗試未找到匹配帳號 (預期: {expected_numbers}, 實際列表: {all_accounts_text})", device=self.device_id)

                    # --- 重試處理 ---
                    if not account_verified and attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    elif not account_verified and attempt == max_retries - 1:
                        # 最後一次嘗試失敗
                        error_message = f"帳號驗證失敗：重試 {max_retries} 次後仍未找到帳號 {expected_numbers}"
                        if account_cells: # 如果最後一次有找到欄位但沒匹配
                             all_accounts = [re.sub(r'[^0-9]', '', cell.inner_text()) for cell in account_cells]
                             error_message += f" (實際帳號列表: {all_accounts})"
                        self.log_with_screenshot('error', error_message, "account_verify_retries_failed")
                        self.stop() # 驗證失敗，停止
                        return False

                except Exception as inner_e:
                    log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(inner_e)}", device=self.device_id)
                    if attempt < max_retries - 1:
                        log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                    else:
                        self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(inner_e)}", "account_verify_error_final")
                        self.stop()
                        return False

            # --- 循環結束後 ---
            if account_verified:
                return True
            else:
                # 理論上如果失敗會在循環內 return False
                log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
                if self.is_running: self.stop() # 確保停止
                return False

        except Exception as e: # 捕捉 verify_account 方法本身的錯誤
            if self.is_running:
                self.log_with_screenshot('error',
                    f"驗證帳號方法執行時發生頂層錯誤: {str(e)}",
                    "account_verify_method_error")
                self.stop()
            return False
            
    def check_and_handle_notice(self):
        """檢查並處理請注意視窗"""
        if not self.is_running:
            return
            
        try:
            # 檢查是否有請注意標題
            notice_heading = self.page.locator("#main").content_frame.get_by_role("heading", name="請注意")
            if notice_heading.is_visible():
                # 點擊繼續使用按鈕
                continue_button = self.page.locator("#main").content_frame.get_by_role("button", name="繼續使用")
                if continue_button.is_visible():
                    continue_button.click()
                    log_manager.debug("處理了請注意視窗", device=self.device_id)
                    
        except Exception as e:
            # 忽略錯誤，因為這是持續監控的操作
            pass
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies"""
        if not self.is_running:
            return
            
        try:
            current_time = time.time()
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update < self.cookie_update_interval:
                return
                
            # 獲取所有 cookies
            cookies = self.context.cookies()
            tibnetbank_session = next(
                (cookie['value'] for cookie in cookies if cookie['name'] == 'TIBNETBANK_SESSION'),
                None
            )
            
            if tibnetbank_session:
                cookies_dict = {"TIBNETBANK_SESSION": tibnetbank_session}
                if self.update_cookies(cookies_dict):
                    self.cookies_updated = True
                    self.last_cookie_update = current_time
                    self.update_status("已更新 cookies")
                else:
                    self.log_with_screenshot('warning', 
                        "更新 cookies 失敗", 
                        "update_cookies_failed")
            else:
                self.log_with_screenshot('warning', 
                    "無法獲取 TIBNETBANK_SESSION", 
                    "session_not_found")
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', 
                    f"獲取 cookies 時發生錯誤: {str(e)}", 
                    "get_cookies_error")
                    
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 直接填入資料，不需要額外的等待
                main_frame = self.page.locator("#main").content_frame
                
                # 填入身分證字號
                id_input = main_frame.get_by_role("textbox", name="身分證字號")
                id_input.fill(self.device_data["bank_id_number"])

                # 填入使用者代號
                user_id_input = main_frame.get_by_role("textbox", name="使用者代號")
                user_id_input.fill(self.device_data["bank_user_id"])

                # 填入使用者密碼
                password_input = main_frame.get_by_role("textbox", name="使用者密碼")
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 台新銀行專用選擇器
                    captcha_selectors = ['#verify img']
                    input_selectors = ['textbox[name="驗證碼"]']

                    if self.handle_captcha_ocr(main_frame, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                    else:
                        log_manager.debug("驗證碼自動識別失敗或無驗證碼", device=self.device_id)
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
                
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟台新銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                self.page.goto(self.login_url)
                self.page.wait_for_load_state("networkidle")
                
                # 等待登入表單出現並嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    # 嘗試自動填入
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:  # 檢查是否已停止
                    break
                    
                self.update_status("登入成功，驗證帳號...")
                
                # 驗證帳號
                if not self.verify_account():
                    break  # verify_account 會自動呼叫 stop()
                    
                self.update_status("帳號驗證成功，開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    try:
                        # 檢查並處理請注意視窗
                        self.check_and_handle_notice()
                        
                        # 更新 cookies
                        self.get_and_update_cookies()
                        
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error', 
                                f"監控過程中發生錯誤: {str(e)}", 
                                "monitor_error")
                            time.sleep(5)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error', 
                        f"執行過程中發生錯誤: {str(e)}", 
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
            
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止台新銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
