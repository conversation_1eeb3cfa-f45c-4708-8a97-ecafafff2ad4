import os
import shutil
import subprocess
import time
import tempfile
import re
from pathlib import Path

def ensure_dir(path):
    """確保目錄存在"""
    if not os.path.exists(path):
        os.makedirs(path)

def safe_remove(path):
    """安全地移除檔案或目錄"""
    max_retries = 3
    retry_delay = 1  # 秒

    for attempt in range(max_retries):
        try:
            if os.path.isfile(path):
                os.unlink(path)
            elif os.path.isdir(path):
                shutil.rmtree(path)
            return True
        except PermissionError:
            if attempt < max_retries - 1:
                print(f"無法刪除 {path}，可能正在使用中。等待 {retry_delay} 秒後重試...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指數退避
            else:
                print(f"警告: 無法刪除 {path}，跳過...")
                return False
        except Exception as e:
            print(f"刪除 {path} 時發生錯誤: {str(e)}")
            return False
    return False

def get_version():
    """讀取版本號"""
    try:
        with open('version.txt', 'r', encoding='utf-8') as f:
            return f.read().strip()
    except Exception as e:
        print(f"讀取版本號失敗: {str(e)}")
        return "1.0.0"  # 預設版本號

def generate_version_module(version):
    """生成版本號模組"""
    version_module = """# 此檔案由建置腳本自動生成
VERSION = "{}"
""".format(version)
    
    # 確保 gui/utils 目錄存在
    ensure_dir('gui/utils')
    
    # 寫入版本號模組
    with open('gui/utils/_version.py', 'w', encoding='utf-8') as f:
        f.write(version_module)

# 移除 get_playwright_browser_root 和 copy_browser_files 函數

def main():
    print("開始建置...")
    
    # 讀取版本號
    version = get_version()
    print(f"當前版本: {version}")
    
    # 生成版本號模組
    generate_version_module(version)
    
    # 檢查必要文件
    required_files = [".env", "main.py", "pyproject.toml", "version.txt"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"錯誤: 找不到必要的文件 {file}")
            return
            
    # 檢查字體目錄
    if not os.path.exists("fonts"):
        print("錯誤: 找不到字體目錄")
        return
        
    # 檢查 gui 目錄
    if not os.path.exists("gui"):
        print("錯誤: 找不到 gui 目錄")
        return
    
    # 清理舊的建置檔案
    paths_to_clean = ['dist', 'build', 'release']
    for path in paths_to_clean:
        if os.path.exists(path):
            print(f"清理 {path}...")
            if not safe_remove(path):
                print(f"警告: 無法完全清理 {path}，繼續建置...")
    
    # 使用 PyInstaller 打包
    print("正在打包程式...")
    try:
        # 檢查 PyInstaller 是否可用 (應該已經在 uv 環境中安裝)
        try:
            result = subprocess.run(['uv', 'run', 'pyinstaller', '--version'],
                                  capture_output=True, text=True, check=True)
            print(f"使用 PyInstaller 版本: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("錯誤: PyInstaller 不可用，請確保已在 uv 環境中安裝")
            return
        
        # 解析版本號
        version_parts = version.split('.')
        version_tuple = tuple(int(p) for p in version_parts + ['0'] * (4 - len(version_parts)))
        
        # 建立版本資訊檔案
        version_content = f"""
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers={version_tuple},
    prodvers={version_tuple},
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Bank Login Manager'),
         StringStruct(u'FileDescription', u'銀行登入管理系統 - 安全的銀行帳戶管理工具'),
         StringStruct(u'FileVersion', u'{version}'),
         StringStruct(u'InternalName', u'bank_login'),
         StringStruct(u'LegalCopyright', u'Copyright (c) 2024 Bank Login Manager'),
         StringStruct(u'OriginalFilename', u'銀行登入管理系統_{version}.exe'),
         StringStruct(u'ProductName', u'銀行登入管理系統'),
         StringStruct(u'ProductVersion', u'{version}'),
         StringStruct(u'Comments', u'安全的銀行帳戶管理工具')])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
        
        # 寫入版本資訊檔案
        with open('file_version_info.txt', 'w', encoding='utf-8') as f:
            f.write(version_content)
            
        # 使用 PyInstaller 打包
        subprocess.run([
            'uv', 'run', 'pyinstaller',
            '--noconfirm',
            '--clean',  # 清理快取
            '--onefile',  # 單一執行檔
            '--windowed',  # 無控制台視窗
            '--uac-admin',  # 要求管理員權限
            '--hidden-import', 'numpy',  # 常用依賴項
            '--hidden-import', 'onnxruntime',  # OCR 依賴
            '--hidden-import', 'onnxruntime.capi',  # ONNX Runtime C API
            '--hidden-import', 'onnxruntime.capi.onnxruntime_pybind11_state',  # ONNX Runtime 綁定
            '--hidden-import', 'onnxruntime.capi._pybind_state',  # ONNX Runtime 綁定狀態
            '--hidden-import', 'cv2',  # OpenCV
            '--hidden-import', 'PIL',  # Pillow
            '--collect-all', 'onnxruntime',  # 強制收集 onnxruntime 的所有內容
            '--collect-binaries', 'onnxruntime',  # 強制收集 onnxruntime 的所有二進制文件
            '--add-data', 'onnxruntime_dlls;onnxruntime/capi',  # 添加 ONNX Runtime DLL 文件
            '--add-data', '.env;.',  # 添加環境檔案
            '--add-data', 'fonts/Noto_Sans_TC/NotoSansTC-VariableFont_wght.ttf;fonts/Noto_Sans_TC',  # 添加字體
            '--add-data', 'fonts/Noto_Sans_TC/static/*.ttf;fonts/Noto_Sans_TC/static',  # 添加字體
            '--add-data', 'gui;gui',  # 添加 GUI 檔案
            '--add-data', 'bank;bank',  # 添加銀行模組
            '--add-data', 'custom_ocr;custom_ocr',  # 添加自定義 OCR 模組
            '--add-data', 'proxy_scripts/rakuten_modify.py;proxy_scripts',  # 添加修改腳本
            '--add-data', 'proxy_scripts/start_proxy.py;proxy_scripts',  # 添加啟動腳本
            '--add-data', 'dependencies;dependencies',  # 添加依賴檔案目錄
            # 移除 Playwright 瀏覽器打包相關的 --add-data
            '--name', f'銀行登入管理系統_{version}',
            '--version-file', 'file_version_info.txt',
            '--disable-windowed-traceback',  # 禁用視窗化錯誤追蹤
            '--noupx',  # 不使用 UPX 壓縮
            'main.py'
        ], check=True)
        
        # 建立發布目錄
        print("正在建立發布套件...")
        try:
            # 確保 release 目錄存在
            ensure_dir('release')
            
            # 複製執行檔
            exe_name = f'銀行登入管理系統_{version}.exe'
            shutil.copy(f'dist/{exe_name}', f'release/{exe_name}')
            print(f"已複製執行檔到 release/{exe_name}")
            
            # 建立其他必要目錄
            ensure_dir(os.path.join('release', 'logs'))
            ensure_dir(os.path.join('release', 'images'))
            
            # 清理建置檔案
            print("清理建置檔案...")
            for path in ['dist', 'build', 'file_version_info.txt', 'gui/utils/_version.py', 'runtime_hook.py']:
                if os.path.exists(path):
                    safe_remove(path)
            
            # 移除 spec 檔案
            if os.path.exists('main.spec'):
                os.unlink('main.spec')
            
            print(f"建置完成！執行檔在 release/{exe_name}")
            
        except Exception as e:
            print(f"建立發布套件時發生錯誤: {str(e)}")
            
    except subprocess.CalledProcessError as e:
        print(f"打包失敗: {str(e)}")
        return
    except Exception as e:
        print(f"打包過程中發生錯誤: {str(e)}")
        return

if __name__ == '__main__':
    main()
