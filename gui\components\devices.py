import tkinter as tk
import threading # 新增導入
from tkinter import ttk
from ttkbootstrap.constants import *
from ttkbootstrap.dialogs import Messagebox
import json
import os
from gui.utils.logger import log_manager
from gui.utils.proxy_tester import run_proxy_test # 導入實際的測速函數
from gui.utils.async_utils import async_utils
from gui.utils.layout_utils import CardLayoutManager
from gui.components.device_card import DeviceCard
from gui.components.dialogs.add_device import AddDeviceDialog
from gui.managers.device_manager import DeviceManager
from dotenv import load_dotenv # 導入 load_dotenv

class DevicesFrame(ttk.Frame):
    """設備管理框架"""
    def __init__(self, parent, app_state):
        super().__init__(parent)
        self.parent = parent
        self.app_state = app_state
        
        # 初始化管理器和狀態
        self.loading = False
        self.device_manager = DeviceManager(app_state)
        self.device_cards = {}
        self.auto_refresh_timer = None
        self.countdown_timer = None
        self.countdown_seconds = 0
        
        # 設置網格布局
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)
        
        # 初始化UI
        self._init_ui()
        self._init_layout_manager()
        self._bind_events()
        
        # 初始加載設備列表
        self.refresh_devices()
        
    def _init_ui(self):
        """初始化UI元件"""
        # 頂部狀態卡片
        self._init_status_bar()
        
        # 設備卡片容器
        self._init_card_container()
        
    def _init_status_bar(self):
        """初始化狀態欄"""
        # 頂部狀態卡片
        status_frame = ttk.Frame(self, style="Card.TFrame")
        status_frame.grid(row=0, column=0, padx=10, pady=(10,5), sticky="ew")
        
        # 左側統計信息
        self._init_statistics(status_frame)
        
        # 右側工具欄
        self._init_toolbar(status_frame)
        
    def _init_statistics(self, parent):
        """初始化統計信息區域"""
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(side=LEFT, padx=15, pady=10)
        
        # 總設備數
        self.total_devices_label = ttk.Label(
            stats_frame,
            text="總設備數：0",
            font=("Noto Sans TC", 12),
            bootstyle="primary"
        )
        self.total_devices_label.pack(side=LEFT, padx=(0,20))
        
        # 在線設備數
        self.online_devices_label = ttk.Label(
            stats_frame,
            text="在線設備：0",
            font=("Noto Sans TC", 12),
            bootstyle="success"
        )
        self.online_devices_label.pack(side=LEFT, padx=(0,20))
        
        # 離線設備數
        self.offline_devices_label = ttk.Label(
            stats_frame,
            text="離線設備：0",
            font=("Noto Sans TC", 12),
            bootstyle="danger"
        )
        self.offline_devices_label.pack(side=LEFT)
        
    def _init_toolbar(self, parent):
        """初始化工具欄"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(side=RIGHT, padx=15, pady=10)

        # --- 代理測速按鈕 ---
        self.proxy_test_button = ttk.Button(
            toolbar_frame,
            text="代理測速",
            command=self.start_proxy_speed_test,
            bootstyle=INFO + OUTLINE, # 使用 INFO 樣式
            width=12
        )
        self.proxy_test_button.pack(side=LEFT, padx=(0, 10)) # 放在最左邊

        # --- 新增設備按鈕 ---
        add_button = ttk.Button(
            toolbar_frame,
            text="新增設備",
            command=self.show_add_device_dialog,
            bootstyle="primary-outline",
            width=12
        )
        add_button.pack(side=LEFT, padx=(0,10)) # 放在測速按鈕右邊

        # --- 刷新按鈕 ---
        self.refresh_button = ttk.Button(
            toolbar_frame,
            text="刷新",
            command=self.refresh_devices,
            bootstyle="primary",  # 使用更顯眼的樣式
            width=8
        )
        self.refresh_button.pack(side=LEFT, padx=(0,10))
        
        # 自動刷新控制
        self._init_auto_refresh(toolbar_frame)
        
    def _init_auto_refresh(self, parent):
        """初始化自動刷新控制"""
        auto_refresh_frame = ttk.Frame(parent)
        auto_refresh_frame.pack(side=LEFT)
        
        # 自動刷新開關
        self.auto_refresh_var = tk.BooleanVar(value=False)
        self.auto_refresh_check = ttk.Checkbutton(
            auto_refresh_frame,
            text="自動刷新",
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh,
            bootstyle="round-toggle"
        )
        self.auto_refresh_check.pack(side=LEFT)
        
        # 間隔選擇
        interval_frame = ttk.Frame(auto_refresh_frame)
        interval_frame.pack(side=LEFT, padx=(10,0))
        
        self.refresh_interval_var = tk.StringVar(value="30")
        self.refresh_interval_combo = ttk.Combobox(
            interval_frame,
            textvariable=self.refresh_interval_var,
            values=["10", "20", "30", "60", "300"],
            state="readonly",
            width=5,
            bootstyle="primary"
        )
        self.refresh_interval_combo.pack(side=LEFT)
        
        ttk.Label(
            interval_frame,
            text="秒",
            bootstyle="secondary"
        ).pack(side=LEFT, padx=(5,0))
        
        # 倒數計時標籤（使用固定寬度）
        countdown_frame = ttk.Frame(interval_frame)
        countdown_frame.pack(side=LEFT, padx=(10,0))
        
        self.countdown_label = ttk.Label(
            countdown_frame,
            text="",
            bootstyle="secondary",
            width=5,  # 設置固定寬度
            anchor="e"  # 右對齊
        )
        self.countdown_label.pack(side=LEFT)
        
        # 初始化時禁用間隔選擇
        self.refresh_interval_combo.configure(state="disabled")
        
    def show_add_device_dialog(self):
        """顯示新增設備對話框"""
        log_manager.debug("顯示新增設備對話框")
        AddDeviceDialog(self)
        
    def _init_card_container(self):
        """初始化卡片容器"""
        # 創建外部容器框架
        container_frame = ttk.Frame(self)
        container_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        container_frame.grid_columnconfigure(0, weight=1)
        container_frame.grid_rowconfigure(0, weight=1)
        
        # 設備卡片容器（使用Canvas實現滾動）
        self.canvas = tk.Canvas(container_frame, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        # 配置滾動框架
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        # 創建視窗並配置滾動
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 設置網格布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        self.scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 確保 canvas 能夠正確展開
        self.canvas.bind('<Configure>', self._on_canvas_configure)
        
    def _on_canvas_configure(self, event):
        """處理 canvas 大小變化"""
        # 設置 scrollable_frame 的寬度與 canvas 相同
        self.canvas.itemconfig(
            self.canvas.find_withtag("all")[0],
            width=event.width
        )
        
    def _init_layout_manager(self):
        """初始化布局管理器"""
        self.layout_manager = CardLayoutManager(
            self.scrollable_frame,
            self.canvas
        )
        
    def _bind_events(self):
        """綁定事件"""
        # 綁定滾輪事件
        self.canvas.bind_all("<MouseWheel>", self._on_mousewheel)
        
        # 綁定自動刷新間隔變更事件
        self.refresh_interval_var.trace("w", self.on_interval_changed)
        
        # 綁定視窗大小變化事件
        self.bind('<Configure>', self._on_frame_configure)
        
        # 綁定主視窗大小變化事件
        self.winfo_toplevel().bind('<Configure>', self._on_window_configure)
        
    def _on_mousewheel(self, event):
        """處理滾輪事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
    def _on_frame_configure(self, event):
        """處理框架大小變化事件"""
        if hasattr(self, 'layout_manager') and hasattr(self, 'device_cards'):
            self.layout_manager.update_layout(self.device_cards)
            
    def _on_window_configure(self, event):
        """處理主視窗大小變化事件"""
        if hasattr(self, 'layout_manager') and hasattr(self, 'device_cards'):
            self.layout_manager.update_window_size(self.device_cards)
        
    def refresh_devices(self):
        """刷新設備列表"""
        if self.loading:
            return
            
        self.set_loading(True)
        log_manager.info("開始刷新設備列表")
        
        async_utils.run_async(
            self.device_manager.fetch_devices,
            self._on_devices_loaded,
            self._on_devices_error
        )
        
    def _on_devices_loaded(self, devices):
        """設備列表加載完成的回調"""
        try:
            self._update_device_list(devices)
        finally:
            self.set_loading(False)
            
    def _update_device_list(self, devices):
        """更新設備列表"""
        try:
            log_manager.info(f"更新設備列表，共 {len(devices)} 個設備")

            # --- 新增：在更新開始時讀取本地設定檔 ---
            local_settings = {"devices": {}}
            settings_path = 'device_settings.json' # 使用與其他部分一致的相對路徑
            if os.path.exists(settings_path):
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        local_settings = json.load(f)
                except json.JSONDecodeError:
                    log_manager.error(f"{settings_path} 格式錯誤，將忽略本地設定。")
                except Exception as e:
                    log_manager.error(f"讀取 {settings_path} 時發生錯誤: {e}")
            # --- 讀取本地設定結束 ---

            # 記錄要移除的卡片
            cards_to_remove = set(self.device_cards.keys())

            # 更新或創建設備卡片
            for device in devices:
                device_id = device["id"]
                if device_id in self.device_cards:
                    # 更新現有卡片
                    card = self.device_cards[device_id] # 獲取現有的卡片實例
                    card.update_device(device) # 使用來自 API 的數據更新卡片基礎資訊
                    cards_to_remove.remove(device_id)
                else:
                    # 創建新卡片
                    card = DeviceCard(
                        self.scrollable_frame,
                        device, # 使用來自 API 的數據進行初始化
                        self._on_device_login,
                        self._on_device_stop
                    )
                    # 注意：DeviceCard 的 __init__ 會調用 load_settings，它會初始讀取本地檔案。
                    self.device_cards[device_id] = card

                # update_device 只負責更新基礎資訊和 auto_fill 的啟用狀態，不修改勾選值。
                # 此處確保勾選值總是反映本地 device_settings.json 的狀態。
                device_local_settings = local_settings.get("devices", {}).get(str(device_id), {})

                # 直接使用本地設定更新自動填入勾選框的值
                local_auto_fill_setting = device_local_settings.get("auto_fill", False)
                card.auto_fill_var.set(local_auto_fill_setting)
                log_manager.debug(f"設備 {device_id}：從本地設定更新自動填入勾選框值為: {local_auto_fill_setting}")

                # 直接使用本地設定更新代理勾選框的值
                local_proxy_setting = device_local_settings.get("use_proxy", False)
                card.use_proxy_var.set(local_proxy_setting)
                log_manager.debug(f"設備 {device_id}：從本地設定更新代理勾選框值為: {local_proxy_setting}")
            
            # 移除不再存在的卡片
            for device_id in cards_to_remove:
                card = self.device_cards.pop(device_id)
                card.destroy()  # 完全銷毀卡片物件
            
            # 更新布局
            self.layout_manager.update_layout(self.device_cards)
            
            # 更新統計信息
            self.update_statistics()
            
            # 更新按鈕狀態
            self.update_device_card_states()
            
            # 更新日誌系統的設備列表
            self._update_logs_frame(devices)
            
        except Exception as e:
            log_manager.exception("更新設備列表時發生錯誤")
            Messagebox.show_error("更新設備列表時發生錯誤", "錯誤")
        
    def update_statistics(self):
        """更新統計信息"""
        stats = self.device_manager.get_statistics()
        self.total_devices_label.configure(text=f"總設備數：{stats['total']}")
        self.online_devices_label.configure(text=f"在線設備：{stats['online']}")
        self.offline_devices_label.configure(text=f"離線設備：{stats['offline']}")
        
    def toggle_auto_refresh(self):
        """切換自動刷新狀態"""
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
            self.refresh_interval_combo.configure(state="readonly")
        else:
            self.stop_auto_refresh()
            self.refresh_interval_combo.configure(state="disabled")
            self.countdown_label.configure(text="")
            
    def update_countdown(self):
        """更新倒數計時顯示"""
        if self.countdown_seconds > 0:
            self.countdown_label.configure(text=f"({self.countdown_seconds})")
            self.countdown_seconds -= 1
            self.countdown_timer = self.after(1000, self.update_countdown)
        else:
            self.countdown_timer = None
            
    def start_auto_refresh(self):
        """開始自動刷新"""
        if self.auto_refresh_timer:
            self.after_cancel(self.auto_refresh_timer)
        if self.countdown_timer:
            self.after_cancel(self.countdown_timer)
            
        interval = int(self.refresh_interval_var.get()) * 1000
        self.refresh_devices()
        
        self.countdown_seconds = int(self.refresh_interval_var.get())
        self.update_countdown()
        
        self.auto_refresh_timer = self.after(interval, self.start_auto_refresh)
        
    def stop_auto_refresh(self):
        """停止自動刷新"""
        if self.auto_refresh_timer:
            self.after_cancel(self.auto_refresh_timer)
            self.auto_refresh_timer = None
        if self.countdown_timer:
            self.after_cancel(self.countdown_timer)
            self.countdown_timer = None
            
    def on_interval_changed(self, *args):
        """刷新間隔變更時重新啟動自動刷新"""
        if self.auto_refresh_var.get():
            self.start_auto_refresh()
            
    def set_loading(self, loading: bool):
        """設置加載狀態"""
        self.loading = loading
        # 只需要禁用按鈕
        self.refresh_button.configure(state="disabled" if loading else "normal")
            
    def update_device_card_states(self):
        """更新所有設備卡片的按鈕狀態"""
        for device_id, card in self.device_cards.items():
            is_running = self.device_manager.is_device_running(device_id)
            card.update_button_states(is_running)
            
    def show_add_device_dialog(self):
        """顯示新增設備對話框"""
        log_manager.debug("顯示新增設備對話框")
        AddDeviceDialog(self)
        
    def _on_devices_error(self, error):
        """設備列表加載失敗的回調"""
        log_manager.error(f"獲取設備列表失敗：{str(error)}")
        Messagebox.show_error("獲取設備列表失敗", "錯誤")
        self.set_loading(False)
        
    def _on_device_login(self, device_id, bank_code, bank_account):
        """處理設備登入"""
        try:
            self.device_manager.start_bank_login(device_id, bank_code, bank_account)
            self.update_device_card_states()
        except Exception as e:
            Messagebox.show_error(str(e), "錯誤")
            
    def _on_device_stop(self, device_id):
        """處理設備停止"""
        try:
            self.device_manager.stop_bank_login(device_id)
            self.update_device_card_states()
        except Exception as e:
            Messagebox.show_error(str(e), "錯誤")
            
    def _update_logs_frame(self, devices):
        """更新日誌框架的設備列表"""
        try:
            app = self.winfo_toplevel()
            if hasattr(app, 'logs_frame'):
                app.logs_frame.update_devices(devices)
        except Exception as e:
            log_manager.error(f"更新日誌設備列表時發生錯誤: {str(e)}")

    # --- 代理測速相關方法 ---

    def _get_default_proxy_test_settings(self):
        """從 device_settings.json 讀取代理測速的預設設定，如果沒有則使用硬編碼預設值"""
        settings = {
            "PROXY_TEST_TIMEOUT_SECONDS": 3.0,
            "PROXY_TEST_KEEP_COUNT": 20,
            "PROXY_TEST_BATCH_SIZE": 5,
            "MAX_TEST_PROXIES": 500
        }

        # 嘗試從 device_settings.json 讀取上次儲存的值作為預設值
        settings_path = 'device_settings.json'
        if os.path.exists(settings_path):
            try:
                with open(settings_path, 'r', encoding='utf-8') as f:
                    device_settings = json.load(f)
                    global_settings = device_settings.get("global", {})
                    for key in settings:
                        if key in global_settings:
                            # 嘗試從 device_settings.json 讀取並轉換類型
                            try:
                                if key == "PROXY_TEST_TIMEOUT_SECONDS":
                                    settings[key] = float(global_settings[key])
                                else:
                                    settings[key] = int(global_settings[key])
                            except ValueError:
                                log_manager.warning(f"無法將 device_settings.json 中的 {key}={global_settings[key]} 轉換為正確的類型，使用硬編碼預設值。")
            except (json.JSONDecodeError, Exception) as e:
                log_manager.error(f"讀取或解析 device_settings.json 檔案時發生錯誤: {e}")

        return settings

    def show_proxy_test_settings_dialog(self):
        """顯示代理測速參數設定視窗"""
        dialog = tk.Toplevel(self.winfo_toplevel())
        dialog.title("代理測速設定")
        dialog.transient(self.winfo_toplevel()) # 設置為模態視窗
        dialog.grab_set() # 阻止與主視窗互動
        dialog.resizable(False, False)

        # 獲取預設值
        default_settings = self._get_default_proxy_test_settings()

        # 創建框架和輸入框
        frame = ttk.Frame(dialog, padding="15")
        frame.pack(fill=BOTH, expand=True)

        self.settings_entries = {} # 用於儲存 Entry 物件

        # 參數列表 (標籤文字, 鍵名, 預設值)
        params = [
            ("測速超時時間 (秒):", "PROXY_TEST_TIMEOUT_SECONDS", default_settings["PROXY_TEST_TIMEOUT_SECONDS"]),
            ("保留的快速代理數量:", "PROXY_TEST_KEEP_COUNT", default_settings["PROXY_TEST_KEEP_COUNT"]),
            ("每次測試的代理數量:", "PROXY_TEST_BATCH_SIZE", default_settings["PROXY_TEST_BATCH_SIZE"]),
            ("最多測試的代理總數:", "MAX_TEST_PROXIES", default_settings["MAX_TEST_PROXIES"]),
        ]

        for i, (label_text, key, default_value) in enumerate(params):
            label = ttk.Label(frame, text=label_text)
            label.grid(row=i, column=0, sticky=W, pady=5, padx=5)

            entry_var = tk.StringVar(value=str(default_value))
            entry = ttk.Entry(frame, textvariable=entry_var, width=20)
            entry.grid(row=i, column=1, sticky=(W, E), pady=5, padx=5)
            self.settings_entries[key] = entry_var # 儲存 StringVar

        # 按鈕框架
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=len(params), column=0, columnspan=2, pady=15)

        start_button = ttk.Button(
            button_frame,
            text="開始測速",
            command=lambda: self._on_start_proxy_test_from_dialog(dialog),
            bootstyle="success"
        )
        start_button.pack(side=LEFT, padx=10)

        cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=dialog.destroy,
            bootstyle="secondary"
        )
        cancel_button.pack(side=LEFT)

        # 設置視窗位置 (居中)
        dialog.update_idletasks()
        x = self.winfo_toplevel().winfo_x() + (self.winfo_toplevel().winfo_width() // 2) - (dialog.winfo_width() // 2)
        y = self.winfo_toplevel().winfo_y() + (self.winfo_toplevel().winfo_height() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

    def _on_start_proxy_test_from_dialog(self, dialog):
        """從設定視窗點擊開始測速按鈕的回調"""
        settings = {}
        try:
            # 獲取並驗證輸入
            settings["PROXY_TEST_TIMEOUT_SECONDS"] = float(self.settings_entries["PROXY_TEST_TIMEOUT_SECONDS"].get())
            settings["PROXY_TEST_KEEP_COUNT"] = int(self.settings_entries["PROXY_TEST_KEEP_COUNT"].get())
            settings["PROXY_TEST_BATCH_SIZE"] = int(self.settings_entries["PROXY_TEST_BATCH_SIZE"].get())
            settings["MAX_TEST_PROXIES"] = int(self.settings_entries["MAX_TEST_PROXIES"].get())

            # 簡單的數值範圍檢查
            if settings["PROXY_TEST_TIMEOUT_SECONDS"] <= 0 or \
               settings["PROXY_TEST_KEEP_COUNT"] <= 0 or \
               settings["PROXY_TEST_BATCH_SIZE"] <= 0 or \
               settings["MAX_TEST_PROXIES"] <= 0:
                Messagebox.show_error("所有參數必須是正數。", "輸入錯誤")
                return

        except ValueError:
            Messagebox.show_error("請輸入有效的數字。", "輸入錯誤")
            return

        # 儲存設定到 device_settings.json
        settings_path = 'device_settings.json'
        try:
            device_settings = {}
            if os.path.exists(settings_path):
                with open(settings_path, 'r', encoding='utf-8') as f:
                    try:
                        device_settings = json.load(f)
                    except json.JSONDecodeError:
                        log_manager.warning(f"{settings_path} 格式錯誤，將重新建立。")
                        device_settings = {}

            if "global" not in device_settings:
                device_settings["global"] = {}

            # 更新 global 設定
            device_settings["global"].update(settings)

            with open(settings_path, 'w', encoding='utf-8') as f:
                json.dump(device_settings, f, indent=2, ensure_ascii=False)
            log_manager.debug("代理測速設定已儲存到 device_settings.json")

        except Exception as e:
            log_manager.error(f"儲存代理測速設定到 device_settings.json 時發生錯誤: {e}")
            Messagebox.show_error(f"儲存設定失敗: {e}", "儲存錯誤")
            # 即使儲存失敗，仍然可以繼續使用當前輸入的設定進行測速

        # 關閉視窗
        dialog.destroy()

        # 啟動測速線程，並傳遞設定參數
        log_manager.info("開始代理測速...")
        self.proxy_test_button.config(state=DISABLED, text="測速中...") # 禁用按鈕並更新文字

        # 在新線程中運行測速，避免阻塞 GUI
        # 將 settings 作為參數傳遞給 _run_test_in_thread
        test_thread = threading.Thread(target=self._run_test_in_thread, args=(settings,), daemon=True)
        test_thread.start()

    # --- 代理測速相關方法 ---
    def start_proxy_speed_test(self):
        """啟動代理測速流程"""
        # 顯示設定視窗，而不是直接啟動測速
        self.show_proxy_test_settings_dialog()

    def _run_test_in_thread(self, settings):
        """在背景線程中執行測速並處理結果"""
        try:
            # --- 調用實際的測速函數 ---
            # 將 settings 作為參數傳遞給 run_proxy_test
            fast_proxies_count = run_proxy_test(settings)
            # --- 測速函數調用結束 ---

            if fast_proxies_count is not None:
                result_message = f"代理測速完成！找到 {fast_proxies_count} 個快速代理。" # 更新提示信息
                log_manager.info(result_message) # 修正縮排
                # 使用 self.after 在主線程顯示 Messagebox
                self.after(0, lambda: Messagebox.show_info(result_message, "測速完成")) # 修正縮排
            else: # 修正縮排
                error_message = "代理測速過程中發生錯誤。" # 修正縮排
                log_manager.error(error_message) # 修正縮排
                self.after(0, lambda: Messagebox.show_error(error_message, "測速失敗")) # 修正縮排

        except Exception as e:
            error_message = f"代理測速執行緒發生未預期錯誤: {e}"
            log_manager.error(error_message)
            self.after(0, lambda: Messagebox.show_error(error_message, "測速錯誤"))
        finally:
            # 無論成功或失敗，都要重新啟用按鈕
            # 需要使用 self.after 來確保在主線程中更新 GUI
            self.after(0, self._enable_proxy_test_button)

    def _enable_proxy_test_button(self):
        """在主線程中重新啟用代理測速按鈕"""
        try:
             if self.proxy_test_button.winfo_exists(): # 檢查按鈕是否存在
                self.proxy_test_button.config(state=NORMAL, text="代理測速")
        except tk.TclError:
             log_manager.warning("嘗試啟用代理測速按鈕時發生 TclError (可能窗口已關閉)")
        except Exception as e:
             log_manager.error(f"啟用代理測速按鈕時發生錯誤: {e}")
    # --- 代理測速相關方法結束 ---

    def destroy(self):
        """銷毀框架時的清理工作"""
        self.stop_auto_refresh()  # 停止自動刷新
        
        # 清理所有設備卡片
        for card in self.device_cards.values():
            card.destroy()
        self.device_cards.clear()
        
        # 調用父類的 destroy
        super().destroy()
