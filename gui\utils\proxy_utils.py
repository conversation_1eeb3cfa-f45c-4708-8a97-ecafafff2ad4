import random
import string
from .logger import log_manager # 假設 logger 在同一層級的 utils 下

def generate_proxy_config(device_id_for_log="N/A"):
    """
    生成隨機的 HTTP 代理配置。

    Args:
        device_id_for_log (str, optional): 用於日誌記錄的設備 ID。預設為 "N/A"。

    Returns:
        dict or None: 包含代理伺服器、用戶名和密碼的字典，如果生成失敗則返回 None。
                      例如: {"server": "http://...", "username": "...", "password": "..."}
    """
    try:
        # 生成 12 位隨機字母數字 session_id
        session_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))

        # 建構代理詳細資訊
        proxy_host = "dfc0c50858f1875d.xuw.as.pyproxy.io"
        proxy_port = 16666
        proxy_user = f"paypay168168-zone-resi-region-tw-session-{session_id}-sessTime-120"
        proxy_pass = "paypay168168"

        proxy_config = {
            "server": f"http://{proxy_host}:{proxy_port}", # 使用 http
            "username": proxy_user,
            "password": proxy_pass
        }
        # 注意：這裡不再記錄日誌，由調用者決定是否記錄
        # log_manager.info(f"生成 HTTP 代理配置: {proxy_config['server']} (使用者: {proxy_user[:15]}... Session: {session_id})", device=device_id_for_log)
        return proxy_config

    except Exception as e:
        log_manager.warning(f"生成代理配置時發生錯誤: {e}。", device=device_id_for_log)
        return None