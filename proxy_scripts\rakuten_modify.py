"""
mitmproxy script for modifying Rakuten Bank's main.js
"""
from mitmproxy import ctx

def response(flow):
    # 只處理 main.js 請求
    if flow.request.pretty_url == "https://www.rakuten-bank.com.tw/ebank/main.js":
        # 修改 JavaScript 內容
        content = flow.response.content.decode('utf-8')
        modified_content = content.replace(
            "ut.enc.Base64.stringify(c);",
            "ut.enc.Base64.stringify(c);window.pppp = p;"
        )
        
        # 設置回應標頭
        flow.response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        flow.response.headers["Pragma"] = "no-cache"
        flow.response.headers["Expires"] = "0"
        
        # 更新回應內容
        flow.response.content = modified_content.encode('utf-8')
        ctx.log.info("已修改 main.js")
