from typing import <PERSON><PERSON>, Dict
import tkinter as tk
from tkinter import ttk

class CardLayoutManager:
    """卡片布局管理器"""
    def __init__(self, 
                 scrollable_frame: ttk.Frame,
                 canvas: tk.Canvas,
                 card_width: int = 240,
                 card_padding: int = 4,
                 scrollbar_width: int = 16,
                 side_padding: int = 4):
        self.scrollable_frame = scrollable_frame
        self.canvas = canvas
        self.card_width = card_width
        self.card_padding = card_padding
        self.scrollbar_width = scrollbar_width
        self.side_padding = side_padding
        self.current_width = None
        self.cards_count = 0
        
    def calculate_layout(self) -> Tuple[int, int]:
        """計算布局參數
        
        Returns:
            Tuple[int, int]: (每行卡片數量, 實際卡片寬度)
        """
        # 獲取實際可用寬度（減去滾動條和邊距）
        canvas_width = self.canvas.winfo_width()
        available_width = canvas_width - self.scrollbar_width - (self.side_padding * 2)
        
        # 計算每行可以放置的卡片數量（考慮卡片寬度和間距）
        total_card_width = self.card_width + self.card_padding
        cards_per_row = max(1, available_width // total_card_width)
        # --- 新增：限制每行最多 4 張卡片 ---
        cards_per_row = min(cards_per_row, 4)
        # --- 限制結束 ---
        
        # 調整卡片實際寬度以充分利用空間
        if cards_per_row > 1:
            # 計算實際使用的寬度（包含間距）
            total_padding = (cards_per_row - 1) * self.card_padding
            actual_card_width = (available_width - total_padding) // cards_per_row
        else:
            actual_card_width = min(self.card_width, available_width)
            
        return cards_per_row, actual_card_width
        
    def update_grid_config(self, cards_per_row: int, card_width: int):
        """更新網格布局配置"""
        # 重置所有列配置
        for i in range(self.scrollable_frame.grid_size()[0]):
            self.scrollable_frame.grid_columnconfigure(i, weight=0)
            
        # 更新網格布局的列配置
        for i in range(cards_per_row):
            self.scrollable_frame.grid_columnconfigure(i, weight=1)
            
    def arrange_cards(self, cards: Dict, cards_per_row: int, card_width: int):
        """重新排列卡片"""
        # 清空現有卡片
        for widget in self.scrollable_frame.winfo_children():
            widget.grid_forget()
            
        # 重新排列卡片
        for i, (device_id, card) in enumerate(cards.items()):
            row = i // cards_per_row
            col = i % cards_per_row
            
            # 設置卡片大小和位置
            card.grid(
                row=row,
                column=col,
                padx=self.card_padding,
                pady=self.card_padding,
                sticky="nsew"
            )
            
    def update_scroll_region(self):
        """更新滾動區域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
    def update_layout(self, cards: Dict):
        """更新整體布局"""
        # 獲取當前寬度和卡片數量
        current_width = self.canvas.winfo_width()
        current_cards_count = len(cards)
        
        # 如果寬度沒有改變且卡片數量沒有改變，則不需要重新布局
        if (self.current_width == current_width and 
            self.cards_count == current_cards_count and 
            self.current_width is not None):
            return
            
        # 更新當前寬度和卡片數量
        self.current_width = current_width
        self.cards_count = current_cards_count
        
        # 計算布局參數
        cards_per_row, card_width = self.calculate_layout()
        
        # 更新網格配置
        self.update_grid_config(cards_per_row, card_width)
        
        # 重新排列卡片
        self.arrange_cards(cards, cards_per_row, card_width)
        
        # 更新滾動區域
        self.update_scroll_region()
        
    def update_window_size(self, cards: Dict):
        """更新視窗大小時重新布局"""
        self.current_width = None  # 重置寬度緩存以強制重新布局
        self.update_layout(cards)
