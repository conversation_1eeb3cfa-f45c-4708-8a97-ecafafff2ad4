from pathlib import Path
import ttkbootstrap as ttk

class ThemeManager:
    @staticmethod
    def setup_theme():
        # 獲取字體路徑
        font_path = Path(__file__).parent.parent.parent / 'fonts' / 'Noto_Sans_TC' / 'static' / 'NotoSansTC-Regular.ttf'
        
        # 創建自定義主題
        my_theme = {
            "type": "dark",
            "colors": {
                "primary": "#007bff",
                "secondary": "#6c757d",
                "success": "#28a745",
                "info": "#17a2b8",
                "warning": "#ffc107",
                "danger": "#dc3545",
                "bg": "#212529",
                "fg": "#f8f9fa",
                "selectbg": "#343a40",
                "selectfg": "#f8f9fa",
                "border": "#343a40",
                "inputfg": "#f8f9fa",
                "inputbg": "#343a40"
            },
            "font": {
                "family": "Noto Sans TC",
                "size": 10,
                "weight": "normal"
            }
        }

        # 創建樣式
        style = ttk.Style()
        
        # 配置通用樣式
        style.configure(".", 
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置按鈕樣式
        style.configure("TButton",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["primary"],
            foreground=my_theme["colors"]["fg"]
        )
        
        style.configure("primary.TButton",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["primary"],
            foreground=my_theme["colors"]["fg"]
        )
        
        style.configure("secondary.TButton",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["secondary"],
            foreground=my_theme["colors"]["fg"]
        )
        
        style.configure("danger.TButton",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["danger"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置標籤樣式
        style.configure("TLabel",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置輸入框樣式
        style.configure("TEntry",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            fieldbackground=my_theme["colors"]["inputbg"],
            foreground=my_theme["colors"]["inputfg"]
        )
        
        # 配置下拉框樣式
        style.configure("TCombobox",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            fieldbackground=my_theme["colors"]["inputbg"],
            foreground=my_theme["colors"]["inputfg"],
            selectbackground=my_theme["colors"]["selectbg"],
            selectforeground=my_theme["colors"]["selectfg"]
        )
        
        # 配置表格樣式
        style.configure("Treeview",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"],
            fieldbackground=my_theme["colors"]["bg"]
        )
        
        # 配置表格標題樣式
        style.configure("Treeview.Heading",
            font=(my_theme["font"]["family"], 11, "bold"),  # 標題字體加大加粗
            background="#2C3E50",  # 深藍色背景
            foreground="white",    # 白色文字
            relief="raised",       # 凸起效果
            borderwidth=1         # 邊框寬度
        )
        
        # 配置框架樣式
        style.configure("TFrame",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"]
        )
        
        style.configure("TLabelframe",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        style.configure("TLabelframe.Label",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置Notebook樣式
        style.configure("TNotebook",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        style.configure("TNotebook.Tab",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["selectbg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置單選按鈕樣式
        style.configure("TRadiobutton",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置複選框樣式
        style.configure("TCheckbutton",
            font=(my_theme["font"]["family"], my_theme["font"]["size"]),
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置標題樣式
        style.configure("Title.TLabel",
            font=(my_theme["font"]["family"], 16, "bold"),  # 標題字體加大加粗
            background=my_theme["colors"]["bg"],
            foreground=my_theme["colors"]["fg"]
        )
        
        # 配置表格標題懸停效果
        style.map("Treeview.Heading",
            background=[("active", "#34495E")],  # 懸停時的深藍色
            foreground=[("active", "white")]     # 懸停時的白色文字
        )
        
        # 配置按鈕懸停效果
        style.map("TButton",
            background=[("active", "#0056b3")],  # 深藍色
            foreground=[("active", my_theme["colors"]["fg"])]
        )
        
        style.map("primary.TButton",
            background=[("active", "#0056b3")],  # 深藍色
            foreground=[("active", my_theme["colors"]["fg"])]
        )
        
        style.map("secondary.TButton",
            background=[("active", "#545b62")],  # 深灰色
            foreground=[("active", my_theme["colors"]["fg"])]
        )
        
        style.map("danger.TButton",
            background=[("active", "#c82333")],  # 深紅色
            foreground=[("active", my_theme["colors"]["fg"])]
        )
        
        return style

    @staticmethod
    def get_font_path():
        return str(Path(__file__).parent.parent.parent / 'fonts' / 'Noto_Sans_TC' / 'static' / 'NotoSansTC-Regular.ttf')
