"""
郵局操作模組 (Playwright 版本)
實作郵局特定的自動化操作
"""

import time
import json
import re
from playwright.sync_api import expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class PostBank(BankBase):
    """郵局操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        # 使用電腦版設定，所以 is_mobile=False
        super().__init__(
            bank_code="700",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=False,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 120  # Cookie 更新間隔（秒）
        self.stored_cookies = None  # 儲存已獲取的 cookies
        self.ws_id = None  # 儲存 WsID
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 等待"我的帳戶"元素出現
            account_element = self.page.locator("div").filter(has_text=re.compile(r"^我的帳戶$"))
            
            # 檢查元素是否可見
            if account_element.is_visible():
                log_manager.debug("檢測到登入成功標誌", device=self.device_id)
                return self.verify_account()
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', 
                    f"檢查登入狀態時發生錯誤: {str(e)}", 
                    "login_check_error")
            return False
            
    def verify_account(self):
        """驗證帳號 (包含重試機制)"""
        max_retries = 3
        retry_delay = 5
        account_verified = False

        for attempt in range(max_retries):
            if not self.is_running: return False # 每次重試前檢查

            log_manager.debug(f"開始驗證郵局帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
            try:
                # 等待表格載入
                table = self.page.locator("#css_table2").first
                try:
                    table.wait_for(state="visible", timeout=10000) # 增加等待時間
                except TimeoutError:
                     log_manager.warning(f"第 {attempt + 1} 次嘗試找不到帳戶表格或超時", device=self.device_id)
                     # 表格找不到，進入重試邏輯
                     if attempt < max_retries - 1:
                         log_manager.info(f"等待 {retry_delay} 秒後重試...", device=self.device_id)
                         for _ in range(retry_delay):
                             if not self.is_running: return False
                             time.sleep(1)
                         continue # 繼續下一次重試
                     else:
                         self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍找不到帳戶表格", "table_not_found_retries_failed")
                         return False

                # 等待存簿帳戶的行載入
                rows = table.locator(".css_tr")
                account_found_in_this_attempt = False

                # 跳過標題行，檢查每一行
                for i in range(1, rows.count()):  # 從1開始跳過標題行
                    row = rows.nth(i)

                    # 檢查是否為存簿帳戶
                    type_cell = row.locator(".css_td.width20").first
                    if "存　　簿" not in type_cell.text_content():
                        continue

                    # 獲取帳號（第二個欄位）
                    account_cell = row.locator(".css_td.width33").first
                    account_text = account_cell.text_content().strip()

                    # 移除破折號和空格後比對
                    clean_account = account_text.replace("-", "").replace(" ", "")
                    if clean_account == self.bank_account:
                        log_manager.info(f"找到匹配的帳號: {account_text} (嘗試 {attempt + 1})", device=self.device_id)
                        account_verified = True
                        account_found_in_this_attempt = True
                        break # 找到帳號，跳出內層循環

                if account_verified:
                    break # 找到帳號，跳出外層重試循環

                # 如果本次嘗試未找到帳號
                if not account_found_in_this_attempt:
                    log_manager.warning(f"第 {attempt + 1} 次嘗試未在表格中找到帳號: {self.bank_account}", device=self.device_id)

                # --- 重試處理 ---
                if not account_verified and attempt < max_retries - 1:
                    log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                elif not account_verified and attempt == max_retries - 1:
                    # 最後一次嘗試失敗
                    self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍未找到帳號 {self.bank_account}", "account_verify_retries_failed")
                    return False

            except Exception as e:
                log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(e)}", device=self.device_id)
                # 發生意外錯誤，處理重試或停止邏輯
                if attempt < max_retries - 1:
                    log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                else:
                    self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(e)}", "account_verify_error_final")
                    return False

        # --- 循環結束後 ---
        if account_verified:
            return True
        else:
            # 理論上如果失敗會在循環內 return False
            log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
            return False
            
    def get_cookies_and_wsid(self):
        """獲取 Cookies 和 WsID"""
        try:
            # 獲取所有 cookies
            cookies = self.context.cookies()
            cookie_string = "; ".join([f"{c['name']}={c['value']}" for c in cookies])
            
            # 從 localStorage 獲取 WsID
            ws_id = self.page.evaluate("() => localStorage.getItem('WsID')")
            
            if not ws_id:
                self.log_with_screenshot('error',
                    "無法獲取 WsID",
                    "wsid_not_found")
                return None
                
            # 組合 cookies 資料
            cookies_data = {
                "Cookies": cookie_string,
                "WsID": ws_id
            }
            
            log_manager.debug(f"成功獲取 Cookies 和 WsID", device=self.device_id)
            return cookies_data
            
        except Exception as e:
            self.log_with_screenshot('error',
                f"獲取 Cookies 和 WsID 時發生錯誤: {str(e)}",
                "get_cookies_error")
            return None
            
    def perform_periodic_tasks(self):
        """執行定時任務：點擊重新計時和更新 cookies"""
        try:
            # 點擊重新計時按鈕
            refresh_button = self.page.get_by_role("link", name="重新計時")
            if refresh_button.is_visible():
                refresh_button.click()
                log_manager.debug("已點擊重新計時按鈕", device=self.device_id)
            
            # 獲取並更新 cookies
            cookies_data = self.get_cookies_and_wsid()
            if cookies_data:
                if self.update_cookies(cookies_data):
                    self.last_cookie_update = time.time()
                    self.update_status("已定時更新 Cookies 和 WsID")
                    log_manager.info("成功定時更新 Cookies 和 WsID 到 API", device=self.device_id)
                    return True
                else:
                    log_manager.error("定時更新 Cookies 和 WsID 失敗", device=self.device_id)
                    return False
            else:
                log_manager.error("無法獲取 Cookies 和 WsID", device=self.device_id)
                return False
                
        except Exception as e:
            log_manager.error(f"執行定時任務時發生錯誤: {str(e)}", device=self.device_id)
            return False
            
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待並獲取輸入欄位
            try:
                # 等待身分證號標籤出現
                try:
                    id_label = self.page.get_by_text("身分證號")
                    id_label.wait_for(state="visible", timeout=5000)
                except TimeoutError:
                    log_manager.debug("自動填入失敗：等待身分證號標籤超時", device=self.device_id)
                    return False
                
                # 使用 ID 定位輸入欄位
                id_input = self.page.locator("#cifID")
                user_id_input = self.page.locator("#userID_1_Input")
                password_input = self.page.locator("#userPWD_1_Input")
                
                # 確保所有欄位都可見
                if not all([id_input.is_visible(), user_id_input.is_visible(), password_input.is_visible()]):
                    log_manager.debug("自動填入失敗：部分輸入欄位未顯示", device=self.device_id)
                    return False
            except Exception as e:
                log_manager.debug(f"自動填入失敗：等待輸入欄位超時 - {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            
            try:
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])

                # 嘗試識別並填入驗證碼 - 使用統一接口
                try:
                    # 郵局專用選擇器
                    captcha_selectors = [
                        'img[src*="captcha"]',  # 通用驗證碼圖片
                        'img[src*="verify"]',
                        'img[src*="code"]',
                        'img[src*="data:image"]',  # base64 圖片
                        'img[alt*="驗證"]',
                        'img[title*="驗證"]'
                    ]
                    input_selectors = [
                        'input[placeholder*="驗證"]',  # 郵局專用
                        'input[name*="captcha"]',
                        'input[name*="verify"]',
                        'input[id*="captcha"]',
                        'input[id*="verify"]'
                    ]

                    if self.handle_captcha_ocr(self.page, captcha_selectors, input_selectors):
                        log_manager.info("驗證碼自動識別並填入成功", device=self.device_id)
                        self.update_status("已自動填入(含驗證碼)")
                    else:
                        log_manager.info("驗證碼自動識別失敗，需要手動輸入", device=self.device_id)
                        self.update_status("已自動填入(需手動驗證碼)")
                except Exception as captcha_e:
                    log_manager.warning(f"驗證碼處理時發生錯誤: {str(captcha_e)}", device=self.device_id)

                log_manager.debug("已自動填入登入表單", device=self.device_id)
                return True
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟郵局網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 前往郵局網頁
                self.page.goto("https://ipost.post.gov.tw/pst/home.html")
                self.page.wait_for_load_state("networkidle")
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                # 首次獲取 cookies
                cookies_data = self.get_cookies_and_wsid()
                if cookies_data:
                    if self.update_cookies(cookies_data):
                        self.cookies_updated = True
                        self.last_cookie_update = time.time()
                        self.update_status("已獲取並更新初始 Cookies 和 WsID")
                        log_manager.info("成功獲取並更新初始 Cookies 和 WsID 到 API", device=self.device_id)
                    else:
                        raise Exception("首次更新 Cookies 和 WsID 失敗")
                else:
                    raise Exception("無法獲取初始 Cookies 和 WsID")
                
                self.update_status("登入成功，開始執行操作...")
                
                # 操作循環
                while self.is_running:
                    try:
                        current_time = time.time()
                        
                        # 檢查是否需要執行定時任務
                        if current_time - self.last_cookie_update >= self.cookie_update_interval:
                            self.perform_periodic_tasks()
                            
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"執行操作時發生錯誤: {str(e)}",
                                "operation_error")
                            self.update_status("發生錯誤，等待重試...")
                            time.sleep(10)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    self.update_status("發生錯誤，等待重試...")
                    time.sleep(10)
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止郵局實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
