"""
銀行操作基礎類別 (Playwright 版本)
提供共用的基礎功能實作
"""

import os
import time
import json
import random # 需要 random 來選擇
import threading
import subprocess
import tempfile
from datetime import datetime
from collections import defaultdict
from playwright.sync_api import sync_playwright
from dotenv import load_dotenv # 需要讀取 .env
from gui.utils.logger import log_manager
from gui.utils.config import config_manager
from gui.utils.proxy_utils import generate_proxy_config
from gui.utils.playwright_utils import get_playwright_browsers_path # 導入新的工具函數
import sys
import shutil # 導入 shutil 用於刪除目錄

# 加載 .env 文件以讀取代理 JSON 文件名
load_dotenv()
FAST_PROXY_JSON_FILE = os.getenv('FAST_PROXY_JSON_FILE', 'proxy.json')

class BankBase:
    """銀行操作基礎類"""
    
    # 每種錯誤類型每天的最大截圖數量
    MAX_SCREENSHOTS_PER_ERROR = 10
    # 保留的天數
    KEEP_DAYS = 3
    
    def __init__(self, bank_code, bank_account, device_id=None, device_data=None, is_mobile=True, use_proxy: bool = False):
        self.bank_code = bank_code
        self.bank_account = bank_account
        self.api_url = config_manager.get_api_url("/api/twd/member/update_cookies")
        self.pending_orders_api_url = config_manager.get_api_url("/api/twd/member/device/pending-orders")
        self.browser = None
        self.page = None
        self.context = None
        self.status_callback = None
        self.is_running = False
        self.device_id = device_id or f"{bank_code}-{bank_account}"
        self.bank_manager = None  # 用於存儲 BankManager 實例的引用
        self.playwright = None  # 保存 playwright 實例的引用
        self.is_mobile = is_mobile  # 是否使用手機版設定
        self.device_data = device_data  # 用於存儲設備資料
        self.should_auto_fill = False  # 是否需要自動填入

        self.use_proxy = use_proxy # 保存 use_proxy 狀態
        self.proxy = None  # 預設不使用代理

        # --- 代理邏輯開始 ---
        effective_device_id = self.device_id # 先取得有效的 device_id
        if self.use_proxy: # 只有在明確要求時才設定代理
            if effective_device_id:
                selected_proxy = None
                # --- 嘗試從 JSON 文件讀取快速代理 ---
                try:
                    if os.path.exists(FAST_PROXY_JSON_FILE):
                        with open(FAST_PROXY_JSON_FILE, 'r', encoding='utf-8') as f:
                            fast_proxies = json.load(f)
                        if fast_proxies and isinstance(fast_proxies, list):
                            # 從列表中隨機選擇一個代理條目
                            chosen_entry = random.choice(fast_proxies)
                            # 提取 proxy_config
                            selected_proxy = chosen_entry.get("proxy_config")
                            if selected_proxy:
                                log_manager.info(f"從 {FAST_PROXY_JSON_FILE} 隨機選取快速代理: {selected_proxy['server']} (User: {selected_proxy['username'][:15]}...) (響應時間: {chosen_entry.get('response_time_seconds', 'N/A')}s)", device=self.device_id)
                            else:
                                log_manager.warning(f"{FAST_PROXY_JSON_FILE} 中的條目格式不正確，缺少 'proxy_config'。")
                        else:
                            log_manager.warning(f"{FAST_PROXY_JSON_FILE} 為空或格式不正確。")
                    else:
                        log_manager.info(f"快速代理文件 {FAST_PROXY_JSON_FILE} 不存在。")
                except json.JSONDecodeError:
                    log_manager.error(f"讀取 {FAST_PROXY_JSON_FILE} 時發生 JSON 解析錯誤。")
                except Exception as e:
                    log_manager.error(f"讀取快速代理文件 {FAST_PROXY_JSON_FILE} 時發生錯誤: {e}")
                # --- 讀取結束 ---

                # --- 如果未從文件選取到代理，則生成新的隨機代理 ---
                if not selected_proxy:
                    log_manager.info("未從文件選取快速代理，將生成新的隨機代理。")
                    selected_proxy = generate_proxy_config(device_id_for_log=self.device_id)
                # --- 生成結束 ---

                # --- 設定最終的代理 ---
                self.proxy = selected_proxy # 無論是選取的還是生成的

                if self.proxy:
                    # 日誌記錄已在選取或生成時完成
                    pass
                else:
                    # 如果選取和生成都失敗
                    log_manager.warning("無法選取或生成代理配置。將不使用代理。", device=self.device_id)
            else:
                # 如果 use_proxy 為 True 但沒有 effective_device_id
                log_manager.warning("需要 device_id 來生成代理設定，但未提供。將不使用代理。", device=self.device_id)
                self.proxy = None
        else:
            log_manager.info("未啟用代理伺服器。", device=self.device_id)
        # --- 代理邏輯結束 ---


        # 確保 images 目錄存在
        if getattr(sys, 'frozen', False):
            # 如果是打包後的執行檔
            base_path = os.path.dirname(sys.executable)
        else:
            # 如果是開發環境
            base_path = os.path.dirname(os.path.dirname(__file__))

        self.images_dir = os.path.join(base_path, 'images')
        os.makedirs(self.images_dir, exist_ok=True)

        # --- 持久化儲存目錄 ---
        self.user_data_dir_base = os.path.join(base_path, 'playwright_cache')
        self.user_data_dir = os.path.join(self.user_data_dir_base, str(self.device_id))
        # os.makedirs(self.user_data_dir, exist_ok=True) # 不在這裡創建，在啟動前清理
        log_manager.debug(f"設定瀏覽器資料儲存目錄: {self.user_data_dir}", device=self.device_id)
        # --- 目錄設定結束 ---

        # 初始化每日錯誤計數器
        self.error_counts = defaultdict(lambda: defaultdict(int))
        # 清理舊的截圖
        self.cleanup_old_screenshots()

    # get_browser_path 方法不再需要，已被 playwright_utils.get_playwright_browsers_path 取代

    def setup_browser(self):
        """設置瀏覽器"""
        try:
            # --- 獲取 Playwright 瀏覽器路徑和執行檔路徑 ---
            ms_playwright_root, chromium_executable_path, headless_executable_path = get_playwright_browsers_path()

            if ms_playwright_root:
                os.environ['PLAYWRIGHT_BROWSERS_PATH'] = ms_playwright_root
                # --- 印出設定的環境變數值 ---
                log_manager.debug(f"已設定 PLAYWRIGHT_BROWSERS_PATH 環境變數為: {os.environ.get('PLAYWRIGHT_BROWSERS_PATH')}", device=self.device_id)
                # --- 印出結束 ---
            else:
                 log_manager.warning("未能獲取 Playwright 瀏覽器根目錄，可能導致啟動失敗。", device=self.device_id)

            if chromium_executable_path:
                 log_manager.debug(f"獲取到 Chromium 瀏覽器執行檔路徑: {chromium_executable_path}", device=self.device_id)
            else:
                 log_manager.warning("未能獲取 Chromium 瀏覽器執行檔路徑。", device=self.device_id)

            if headless_executable_path:
                 log_manager.debug(f"獲取到 Chromium Headless Shell 執行檔路徑: {headless_executable_path}", device=self.device_id)
            else:
                 log_manager.warning("未能獲取 Chromium Headless Shell 執行檔路徑。", device=self.device_id)
            # --- 獲取結束 ---


            # --- 清理舊的持久化上下文目錄 ---
            if os.path.exists(self.user_data_dir):
                log_manager.debug(f"清理舊的瀏覽器資料儲存目錄: {self.user_data_dir}", device=self.device_id)
                try:
                    shutil.rmtree(self.user_data_dir)
                    log_manager.debug("舊的瀏覽器資料儲存目錄清理完成。", device=self.device_id)
                except Exception as e:
                    log_manager.error(f"清理舊的瀏覽器資料儲存目錄失敗: {e}", device=self.device_id)
            # --- 清理結束 ---

            self.playwright = sync_playwright().start()
            log_manager.debug("Playwright 啟動成功", device=self.device_id)
            
            # 瀏覽器設定
            browser_args = [
                '--disable-dev-shm-usage',
                '--no-sandbox',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-blink-features=AutomationControlled',  # 禁用自動化檢測
                # '--incognito', # 移除無痕模式，改用持久化上下文
                '--password-store=basic',  # 禁用密碼管理器
                '--disable-background-networking',  # 禁用背景網絡
                '--disable-background-timer-throttling',  # 禁用背景計時器限制
                '--disable-backgrounding_occluded_windows',  # 禁用背景窗口限制
                '--disable-breakpad',  # 禁用崩潰報告
                '--disable-client-side_phishing_detection',  # 禁用釣魚檢測
                '--disable-component-update',  # 禁用組件更新
                '--disable-default-apps',  # 禁用默認應用
                '--disable-domain-reliability',  # 禁用域名可靠性
                '--disable-extensions',  # 禁用擴展
                '--disable-features=TranslateUI',  # 禁用翻譯
                '--disable-hang-monitor',  # 禁用掛起監視器
                '--disable-ipc-flooding-protection',  # 禁用 IPC 洪水保護
                '--disable-popup-blocking',  # 禁用彈出窗口阻止
                '--disable-prompt-on_repost',  # 禁用重新發布提示
                '--disable-renderer-backgrounding',  # 禁用渲染器背景
                '--disable-sync',  # 禁用同步
                '--force-color-profile=srgb',  # 強制顏色配置
                '--metrics-recording-only',  # 僅記錄指標
                '--no-first-run',  # 禁用首次運行
                '--safebrowsing-disable-auto-update',  # 禁用安全瀏覽自動更新
            ]

            # 根據是否為手機版設定不同的 User-Agent
            if self.is_mobile:
                persistent_context_args = {
                    'viewport': {'width': 375, 'height': 812},
                    'device_scale_factor': 3.0,
                    'is_mobile': True,
                    'has_touch': True,
                    'user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
                    'headless': False, # 根據需要設置
                    'args': browser_args,
                    'channel': None, # 使用內建瀏覽器
                    'handle_sigint': True,
                    'handle_sigterm': True,
                    'handle_sighup': True,
                    'ignore_https_errors': True,
                    'java_script_enabled': True,
                    'bypass_csp': True,
                    'accept_downloads': False,
                    'offline': False,
                    'permissions': [],
                    'proxy': self.proxy,
                    'record_har_path': None,
                    'record_video_dir': None,
                    'reduced_motion': 'reduce',
                    'strict_selectors': True,
                    'service_workers': 'block'
                }
            else:
                persistent_context_args = {
                    'viewport': {'width': 1280, 'height': 800},
                    'device_scale_factor': 1.0,
                    'is_mobile': False,
                    'has_touch': False,
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    'headless': False, # 根據需要設置
                    'args': browser_args,
                    'channel': None, # 使用內建瀏覽器
                    'handle_sigint': True,
                    'handle_sigterm': True,
                    'handle_sighup': True,
                    'ignore_https_errors': True,
                    'java_script_enabled': True,
                    'bypass_csp': True,
                    'accept_downloads': False,
                    'offline': False,
                    'permissions': [],
                    'proxy': self.proxy,
                    'record_har_path': None,
                    'record_video_dir': None,
                    'reduced_motion': 'reduce',
                    'strict_selectors': True,
                    'service_workers': 'block'
                }
            # --- 參數準備結束 ---

            # --- 如果獲取到 Chromium 執行檔路徑，則添加到參數中 ---
            if chromium_executable_path:
                 persistent_context_args['executable_path'] = chromium_executable_path
                 log_manager.debug(f"將 executable_path 設定為: {chromium_executable_path}", device=self.device_id)
            # --- 添加結束 ---


            # --- 啟動持久化上下文 ---
            try:
                log_manager.debug(f"嘗試使用 user_data_dir '{self.user_data_dir}' 啟動持久化上下文...", device=self.device_id)
                self.context = self.playwright.chromium.launch_persistent_context(
                    self.user_data_dir,
                    **persistent_context_args
                )
                log_manager.debug("持久化瀏覽器上下文創建成功", device=self.device_id)

                # 創建頁面 (從持久化上下文中獲取第一個頁面，如果沒有則創建新的)
                if self.context.pages:
                    self.page = self.context.pages[0]
                    log_manager.debug("從現有上下文中獲取頁面", device=self.device_id)
                else:
                    self.page = self.context.new_page()
                    log_manager.debug("創建新頁面", device=self.device_id)
                
                # 設置請求攔截
                self.page.on('request', self._handle_request)
                self.page.on('response', self._handle_response)
                
                log_manager.debug("瀏覽器頁面創建成功", device=self.device_id)
                # --- 測試代理 IP (如果已設定) ---
                if self.proxy:
                    try:
                        # 改用 api.ipify.org，它直接返回純文字 IP
                        test_ip_url = "https://api.ipify.org"
                        log_manager.debug(f"嘗試透過代理訪問 {test_ip_url} 測試 IP...", device=self.device_id)
                        # 使用較短的超時時間進行測試
                        self.page.goto(test_ip_url, timeout=20000)
                        # 直接獲取 body 內容，因為 api.ipify.org 只返回 IP
                        ip_address = self.page.inner_text('body', timeout=5000).strip()
                        
                        # 簡單驗證是否為 IP 格式 (可選，增加穩健性)
                        import re
                        if re.match(r"^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$", ip_address):
                            log_manager.info(f"透過代理取得的 IP: {ip_address}", device=self.device_id)
                        else:
                            log_manager.warning(f"從 {test_ip_url} 獲取的內容似乎不是標準 IP 格式: {ip_address}", device=self.device_id)
                            # 即使格式不符，仍然記錄獲取的內容，以供調試
                            log_manager.info(f"透過代理取得的原始內容: {ip_address}", device=self.device_id)
                    except Exception as ip_err:
                        log_manager.warning(f"測試代理 IP 時發生錯誤: {ip_err}", device=self.device_id)
                        # 即使測試失敗，也繼續執行，但記錄警告
                # --- 測試代理 IP 結束 ---
                
            except Exception as e:
                log_manager.error(f"啟動瀏覽器失敗: {str(e)}", device=self.device_id)
                raise
            
        except Exception as e:
            self.log_with_screenshot('error', f"設置瀏覽器時發生錯誤: {str(e)}", "browser_setup_error")
            raise
            
    def _handle_request(self, request):
        """處理請求事件"""
        if not self.is_running:
            return
            
        try:
            # 記錄重要的請求
            if any(keyword in request.url for keyword in ['login', 'auth', 'token']):
                log_manager.debug(f"攔截到重要請求: {request.url}", device=self.device_id)
        except Exception as e:
            log_manager.error(f"處理請求時發生錯誤: {str(e)}", device=self.device_id)
            
    def _handle_response(self, response):
        """處理響應事件"""
        if not self.is_running:
            return
            
        try:
            # 記錄重要的響應
            if response.status == 200 and any(keyword in response.url for keyword in ['login', 'auth', 'token']):
                log_manager.debug(f"攔截到重要響應: {response.url} (狀態碼: {response.status})", device=self.device_id)
        except Exception as e:
            log_manager.error(f"處理響應時發生錯誤: {str(e)}", device=self.device_id)
            
    def get_today_date(self):
        """獲取今天的日期字符串"""
        return datetime.now().strftime('%Y%m%d')
        
    def get_error_screenshots(self, reason):
        """獲取特定錯誤類型當天的所有截圖"""
        today = self.get_today_date()
        screenshots = []
        
        try:
            for filename in os.listdir(self.images_dir):
                if not filename.endswith('.png'):
                    continue
                    
                try:
                    date_str = filename.split('-')[2][:8]  # 獲取 YYYYMMDD 部分
                    if date_str != today:
                        continue
                        
                    if filename.split('-')[-1].replace('.png', '') == reason:
                        filepath = os.path.join(self.images_dir, filename)
                        timestamp = datetime.strptime(filename.split('-')[2], '%Y%m%d_%H%M%S')
                        screenshots.append((filepath, timestamp))
                        
                except (ValueError, IndexError):
                    continue
                    
            screenshots.sort(key=lambda x: x[1])
            return screenshots
            
        except Exception as e:
            log_manager.error(f"獲取錯誤截圖列表時發生錯誤: {str(e)}", device=self.device_id)
            return []
            
    def manage_error_screenshots(self, reason):
        """管理特定錯誤類型的截圖數量"""
        screenshots = self.get_error_screenshots(reason)
        
        if len(screenshots) >= self.MAX_SCREENSHOTS_PER_ERROR:
            try:
                oldest_file, _ = screenshots[0]
                os.remove(oldest_file)
                log_manager.debug(f"已刪除最舊的截圖: {oldest_file}", device=self.device_id)
                return True
            except Exception as e:
                log_manager.error(f"刪除舊截圖時發生錯誤: {str(e)}", device=self.device_id)
                return False
                
        return True
        
    def cleanup_old_screenshots(self):
        """清理舊的截圖檔案"""
        try:
            current_date = datetime.now()
            
            for filename in os.listdir(self.images_dir):
                if not filename.endswith('.png'):
                    continue
                    
                filepath = os.path.join(self.images_dir, filename)
                
                try:
                    date_str = filename.split('-')[2]
                    file_date = datetime.strptime(date_str, '%Y%m%d_%H%M%S')
                    
                    if (current_date - file_date).days > self.KEEP_DAYS:
                        os.remove(filepath)
                        log_manager.debug(f"已刪除舊截圖: {filename}", device=self.device_id)
                        
                except (ValueError, IndexError):
                    log_manager.warning(f"無法解析截圖檔案日期: {filename}", device=self.device_id)
                    
        except Exception as e:
            log_manager.error(f"清理舊截圖時發生錯誤: {str(e)}", device=self.device_id)
            
    def take_screenshot(self, reason):
        """進行截圖並保存"""
        if not self.page:
            return None
            
        try:
            if not self.manage_error_screenshots(reason):
                log_manager.error(f"無法管理 {reason} 類型的截圖", device=self.device_id)
                return None
                
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{self.bank_code}-{self.bank_account}-{timestamp}-{reason}.png"
            filepath = os.path.join(self.images_dir, filename)
            
            self.page.screenshot(path=filepath, full_page=True)
            return filepath
            
        except Exception as e:
            log_manager.error(f"截圖時發生錯誤: {str(e)}", device=self.device_id)
            return None
            
    def log_with_screenshot(self, level, message, reason):
        """記錄日誌並同時進行截圖"""
        screenshot_path = self.take_screenshot(reason)
        
        full_message = f"{message} (截圖: {screenshot_path})" if screenshot_path else f"{message} (截圖失敗)"
        
        if level == 'warning':
            log_manager.warning(full_message, device=self.device_id)
        elif level == 'error':
            log_manager.error(full_message, device=self.device_id)
            
    def set_bank_manager(self, bank_manager):
        """設置 BankManager 實例"""
        self.bank_manager = bank_manager
        
    def set_status_callback(self, callback):
        """設置狀態回調函數"""
        self.status_callback = callback
        
    def update_status(self, status):
        """更新狀態"""
        if self.is_running and self.status_callback:
            self.status_callback(status)
            
    def update_cookies(self, cookies_data):
        """更新 cookies 到 API"""
        if not self.is_running:
            return False
            
        try:
            payload = {
                "bank_code": self.bank_code,
                "bank_account": str(self.bank_account),
                "cookies": cookies_data
            }
            
            # 使用 Playwright 的請求功能
            headers = {
                'Content-Type': 'application/json'
            }
            
            # 如果有 bank_manager 且其 device_manager 有 app_state，則加入 Authorization header
            if (self.bank_manager and
                hasattr(self.bank_manager, 'device_manager') and
                hasattr(self.bank_manager.device_manager, 'app_state') and
                hasattr(self.bank_manager.device_manager.app_state, 'current_token')):
                headers['Authorization'] = self.bank_manager.device_manager.app_state.current_token
            
            response = self.page.request.post(
                self.api_url,
                data=json.dumps(payload),
                headers=headers
            )
            
            if response.status != 200:
                error_message = f"更新 cookies 失敗，HTTP 狀態碼: {response.status}\n回應內容: {response.text()}"
                self.log_with_screenshot('error', error_message, "update_cookies_failed")
                
                # 檢查是否為 401 錯誤
                try:
                    data = response.json()
                    if response.status == 401 or (data.get("code") == 401 and data.get("message") == "登入已過期，請重新登入"):
                        log_manager.error("登入已過期，請重新登入", device=self.device_id)
                        # 通知 bank_manager 停止所有銀行實例
                        if self.bank_manager:
                            self.bank_manager.stop_current_bank()
                            
                except:
                    pass
                    
                return False
                
            try:
                data = response.json()
                log_manager.debug(f"更新 cookies API 回應: {json.dumps(data, indent=2)}", device=self.device_id)
                
                if data.get("code") == 0 and data.get("msg") == "更新成功":
                    log_manager.debug("成功更新 cookies 到 API", device=self.device_id)
                    return True
                else:
                    self.log_with_screenshot('error',
                        f"更新 cookies 失敗，回應內容: {data}",
                        "update_cookies_failed")
                    return False
                    
            except json.JSONDecodeError as e:
                self.log_with_screenshot('error',
                    f"解析回應內容時發生錯誤: {str(e)}\n原始回應: {response_text}",
                    "update_cookies_parse_error")
                return None
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"更新 cookies 時發生錯誤: {str(e)}",
                    "update_cookies_error")
            return False

    def check_auto_fill_settings(self):
        """檢查自動填入設定"""
        try:
            import json
            import os
            settings_path = 'device_settings.json'
            
            # 讀取設定檔
            with open(settings_path, 'r', encoding='utf-8') as f:
                settings = json.load(f)
            
            # 讀取設備設定
            device_id = str(self.device_id)  # 確保 ID 是字串
            device_settings = settings.get('devices', {}).get(device_id, {"auto_fill": False})
            self.should_auto_fill = device_settings.get("auto_fill", False)
            
            # 如果啟用自動填入，則檢查設備資料
            if self.should_auto_fill:
                if not self.device_data:
                    log_manager.error("無設備資料，自動填入將被停用", device=self.device_id)
                    self.should_auto_fill = False
                else:
                    # 檢查必要的欄位是否存在
                    required_fields = ['bank_id_number', 'bank_user_id', 'bank_user_password']
                    if not all(field in self.device_data for field in required_fields):
                        log_manager.error("設備資料缺少必要欄位，自動填入將被停用", device=self.device_id)
                        self.should_auto_fill = False
                    else:
                        log_manager.debug(f"已載入設備 {device_id} 的自動填入設定: {self.should_auto_fill}", device=self.device_id)
                        
        except Exception as e:
            log_manager.error(f"讀取自動填入設定時發生錯誤: {str(e)}", device=self.device_id)
            self.should_auto_fill = False
            
    def auto_fill_login_form(self):
        """自動填入登入表單 (由子類別實作)"""
        pass

    def get_pending_orders(self, device_id):
        """查詢設備的支付中訂單"""
        if not self.is_running:
            return None
            
        try:
            # 準備請求參數
            params = {
                'device_id': device_id,
                'status': 0  # 支付中狀態
            }
            
            # 準備 headers
            headers = {
                'Content-Type': 'application/json'
            }
            
            # 如果有 token 則加入 Authorization header
            if (self.bank_manager and
                hasattr(self.bank_manager, 'device_manager') and
                hasattr(self.bank_manager.device_manager, 'app_state') and
                hasattr(self.bank_manager.device_manager.app_state, 'current_token')):
                headers['Authorization'] = self.bank_manager.device_manager.app_state.current_token
            
            # 構建完整的 URL
            url = f"{self.pending_orders_api_url}?device_id={device_id}&status=0"
            
            # 發送請求
            response = self.page.request.get(
                url,
                headers=headers
            )
            
            if response.status != 200:
                error_message = f"查詢支付中訂單失敗，HTTP 狀態碼: {response.status}\n回應內容: {response.text()}"
                self.log_with_screenshot('error', error_message, "get_pending_orders_failed")
                
                # 檢查是否為 401 錯誤
                try:
                    data = response.json()
                    if response.status == 401 or (data.get("code") == 401 and data.get("message") == "登入已過期，請重新登入"):
                        log_manager.error("登入已過期，請重新登入", device=self.device_id)
                        # 通知 bank_manager 停止所有銀行實例
                        if self.bank_manager:
                            self.bank_manager.stop_current_bank()
                            
                except:
                    pass
                    
                return None
                
            try:
                data = response.json()
                log_manager.debug(f"查詢設備的支付中訂單 API 回應: {json.dumps(data, indent=2)}", device=self.device_id)
                
                if data.get("code") == 0 and data.get("msg") == "success":
                    log_manager.debug("成功查詢支付中訂單", device=self.device_id)
                    return data.get("data", [])
                else:
                    self.log_with_screenshot('error',
                        f"查詢支付中訂單失敗，回應內容: {data}",
                        "get_pending_orders_failed")
                    return None
                    
            except json.JSONDecodeError as e:
                self.log_with_screenshot('error',
                    f"解析回應內容時發生錯誤: {str(e)}\n原始回應: {response_text}",
                    "get_pending_orders_parse_error")
                return None
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"查詢支付中訂單時發生錯誤: {str(e)}",
                    "get_pending_orders_error")
            return None

            
    def stop(self):
        """停止運行 (由子類別實作)"""
        log_manager.debug("開始停止銀行實例", device=self.device_id)
        self.is_running = False
        # 確保關閉 context 而不是 browser
        if self.page:
            try:
                self.page.close()
                log_manager.debug("頁面已關閉", device=self.device_id)
            except Exception as e:
                log_manager.warning(f"關閉頁面時發生錯誤: {e}", device=self.device_id)
            self.page = None
        if self.context:
            try:
                self.context.close()
                log_manager.debug("瀏覽器上下文已關閉", device=self.device_id)
            except Exception as e:
                log_manager.warning(f"關閉瀏覽器上下文時發生錯誤: {e}", device=self.device_id)
            self.context = None
        # browser 實例不再由 BasePlaywright 直接管理
        # if self.browser:
        #     try:
        #         self.browser.close()
        #         log_manager.debug("瀏覽器已關閉", device=self.device_id)
        #     except Exception as e:
        #         log_manager.warning(f"關閉瀏覽器時發生錯誤: {e}", device=self.device_id)
        #     self.browser = None
        if self.playwright:
            try:
                self.playwright.stop()
                log_manager.debug("Playwright 實例已停止", device=self.device_id)
            except Exception as e:
                log_manager.warning(f"停止 Playwright 時發生錯誤: {e}", device=self.device_id)
            self.playwright = None
        log_manager.debug("銀行實例停止完成", device=self.device_id)

    # ==================== OCR 統一接口 ====================

    def handle_captcha_ocr(self, frame, captcha_selectors=None, input_selectors=None, max_retries=2):
        """
        統一的驗證碼 OCR 處理接口

        Args:
            frame: Playwright 框架對象（可以是 page 或 frame）
            captcha_selectors: 驗證碼圖片選擇器列表，如果為 None 則使用預設選擇器
            input_selectors: 驗證碼輸入框選擇器列表，如果為 None 則使用預設選擇器
            max_retries: 最大重試次數

        Returns:
            bool: 成功返回 True，失敗返回 False
        """
        try:
            from bank.ocr_utils import captcha_ocr

            # 檢查 OCR 功能是否可用
            if not captcha_ocr.is_available():
                log_manager.info("OCR 功能不可用，跳過自動識別", device=self.device_id)
                return False

            # 查找驗證碼元素
            captcha_img, captcha_input = self.find_captcha_elements(frame, captcha_selectors, input_selectors)

            if not captcha_img or not captcha_input:
                log_manager.debug("未找到驗證碼圖片或輸入框", device=self.device_id)
                return False

            # 識別並填入驗證碼
            return self.recognize_and_fill_captcha(captcha_img, captcha_input, max_retries)

        except Exception as e:
            log_manager.warning(f"驗證碼 OCR 處理時發生錯誤: {str(e)}", device=self.device_id)
            return False

    def find_captcha_elements(self, frame, captcha_selectors=None, input_selectors=None):
        """
        查找驗證碼圖片和輸入框元素

        Args:
            frame: Playwright 框架對象
            captcha_selectors: 驗證碼圖片選擇器列表
            input_selectors: 驗證碼輸入框選擇器列表

        Returns:
            tuple: (captcha_img_element, captcha_input_element) 或 (None, None)
        """
        try:
            # 通用的驗證碼圖片選擇器（不包含銀行專用選擇器）
            default_captcha_selectors = [
                'img[src*="captcha"]',
                'img[src*="verify"]',
                'img[src*="code"]',
                'img[alt*="驗證"]',
                'img[title*="驗證"]',
                'img[id*="captcha"]',
                'img[id*="verify"]',
                'img[class*="captcha"]',
                'img[class*="verify"]'
            ]

            # 通用的驗證碼輸入框選擇器（不包含銀行專用選擇器）
            default_input_selectors = [
                'input[placeholder*="驗證"]',
                'input[name*="captcha"]',
                'input[name*="verify"]',
                'input[id*="captcha"]',
                'input[id*="verify"]',
                'input[class*="captcha"]',
                'input[class*="verify"]'
            ]

            # 使用提供的選擇器或預設選擇器
            captcha_selectors = captcha_selectors or default_captcha_selectors
            input_selectors = input_selectors or default_input_selectors

            # 查找驗證碼圖片
            captcha_img = None
            for selector in captcha_selectors:
                try:
                    # 處理特殊的複合選擇器
                    if selector.startswith('generic:has('):
                        # 解析 generic:has(textbox[placeholder="驗證碼"]) img 格式
                        # 提取內部選擇器和目標元素
                        if ') img' in selector:
                            inner_selector = selector.split('generic:has(')[1].split(')')[0]

                            # 處理 textbox role-based 選擇器
                            if inner_selector.startswith('textbox[') and 'placeholder=' in inner_selector:
                                placeholder_text = inner_selector.split('placeholder="')[1].split('"')[0]
                                # 找到包含指定 textbox 的容器，然後在其中找 img
                                textbox_element = frame.get_by_role("textbox", name=placeholder_text)
                                if textbox_element.is_visible(timeout=1000):
                                    # 找到 textbox 的父容器，然後在其中查找 img
                                    parent_locator = textbox_element.locator('xpath=..')
                                    element = parent_locator.locator('img').first
                                else:
                                    continue
                            else:
                                # 其他類型的內部選擇器
                                container = frame.locator(inner_selector).first
                                if container.is_visible(timeout=1000):
                                    element = container.locator('img').first
                                else:
                                    continue
                        else:
                            continue
                    else:
                        # 標準選擇器處理
                        element = frame.locator(selector).first

                    if element.is_visible(timeout=1000):
                        captcha_img = element
                        log_manager.debug(f"找到驗證碼圖片，使用選擇器: {selector}", device=self.device_id)
                        break
                except Exception as e:
                    log_manager.debug(f"選擇器 {selector} 失敗: {str(e)}", device=self.device_id)
                    continue

            # 查找驗證碼輸入框
            captcha_input = None
            for selector in input_selectors:
                try:
                    if selector.startswith('textbox['):
                        # 處理 role-based 選擇器
                        if 'name=' in selector:
                            role_name = selector.split('name="')[1].split('"')[0]
                            element = frame.get_by_role("textbox", name=role_name)
                        elif 'placeholder=' in selector:
                            placeholder_text = selector.split('placeholder="')[1].split('"')[0]
                            element = frame.get_by_role("textbox", name=placeholder_text)
                        else:
                            element = frame.locator(selector).first
                    else:
                        element = frame.locator(selector).first

                    if element.is_visible(timeout=1000):
                        captcha_input = element
                        log_manager.debug(f"找到驗證碼輸入框，使用選擇器: {selector}", device=self.device_id)
                        break
                except:
                    continue

            # 調試信息：報告查找結果
            if captcha_img is None:
                log_manager.debug(f"未找到驗證碼圖片，嘗試的選擇器: {captcha_selectors}", device=self.device_id)
            if captcha_input is None:
                log_manager.debug(f"未找到驗證碼輸入框，嘗試的選擇器: {input_selectors}", device=self.device_id)

            return captcha_img, captcha_input

        except Exception as e:
            log_manager.warning(f"查找驗證碼元素時發生錯誤: {str(e)}", device=self.device_id)
            return None, None

    def recognize_and_fill_captcha(self, captcha_img, captcha_input, max_retries=2):
        """
        識別並填入驗證碼

        Args:
            captcha_img: 驗證碼圖片元素
            captcha_input: 驗證碼輸入框元素
            max_retries: 最大重試次數

        Returns:
            bool: 成功返回 True，失敗返回 False
        """
        try:
            from bank.ocr_utils import captcha_ocr

            log_manager.info("開始 OCR 識別驗證碼...", device=self.device_id)

            # 使用 OCR 識別驗證碼
            result = captcha_ocr.recognize_from_element(captcha_img, device_id=self.device_id, max_retries=max_retries)

            if result and len(result) >= 3:  # 驗證結果合理性
                # 填入驗證碼
                captcha_input.fill(result, timeout=5000)
                log_manager.info(f"驗證碼自動識別並填入成功: {result}", device=self.device_id)
                return True
            else:
                log_manager.warning(f"驗證碼識別結果不合理: {result}", device=self.device_id)
                return False

        except Exception as e:
            log_manager.error(f"識別並填入驗證碼時發生錯誤: {str(e)}", device=self.device_id)
            return False
