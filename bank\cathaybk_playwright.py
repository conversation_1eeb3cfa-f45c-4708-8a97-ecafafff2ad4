"""
國泰世華銀行操作模組 (Playwright 版本)
實作國泰世華銀行特定的自動化操作
"""

import time
import json
from playwright.sync_api import expect
from gui.utils.logger import log_manager
from .base_playwright import BankBase

class CathayBank(BankBase):
    """國泰世華銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=False, use_proxy: bool = False): # 新增 is_mobile 和 use_proxy 參數
        super().__init__(
            bank_code="013",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=is_mobile,  # 支援手機版設定
            use_proxy=use_proxy
        )
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # Cookie 更新間隔（秒）
        self.stored_cookies = None  # 儲存已獲取的 cookies
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 檢查並點擊"我的帳戶總覽"
            overview_button = self.page.get_by_text("我的帳戶總覽")
            if not overview_button.is_visible():
                return False
                
            overview_button.click()
            log_manager.debug("點擊我的帳戶總覽", device=self.device_id)
            
            # 等待頁面載入完成
            self.page.wait_for_load_state("networkidle")
            
            # 檢查帳號連結是否可見
            account_number = self.bank_account.lstrip('0')
            account_link = self.page.get_by_role("link", name=account_number)
            
            # 檢查帳號連結是否可見 (最多 3 次嘗試，每次間隔 5 秒)
            check_attempts = 0
            max_check_attempts = 3
            account_visible = False
            while self.is_running and check_attempts < max_check_attempts:
                check_attempts += 1
                log_manager.debug(f"檢查帳號連結可見性... (嘗試 {check_attempts}/{max_check_attempts})", device=self.device_id)
                if account_link.is_visible(timeout=5000): # 增加短暫超時檢查
                    account_visible = True
                    break # 找到連結，跳出嘗試迴圈

                # 找不到連結，等待後重試 (除非是最後一次嘗試)
                if check_attempts < max_check_attempts:
                    log_manager.warning(f"找不到帳號連結 {account_number}，將在 5 秒後重試 (嘗試 {check_attempts}/{max_check_attempts})", device=self.device_id)
                    time.sleep(5)
                else:
                    # 達到最大嘗試次數
                    log_manager.error(f"在 {max_check_attempts} 次嘗試後仍找不到帳號連結: {account_number}", device=self.device_id)

            # 根據最終檢查結果執行操作
            if account_visible:
                # 點擊帳號連結
                account_link.click()
                log_manager.debug(f"成功點擊帳號連結: {account_number}", device=self.device_id)
                self.page.wait_for_load_state("networkidle")
                return True
            else:
                self.log_with_screenshot('error',
                    f"找不到預期的帳號連結: {account_number} ({max_check_attempts} 次嘗試失敗)",
                    "account_not_found")
                return False
                
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', f"檢查登入狀態時發生錯誤: {str(e)}", "login_check_error")
            return False
            
    def _handle_request(self, request):
        """處理請求事件，只在首次獲取 cookies"""
        if not self.is_running or self.stored_cookies is not None:
            return
            
        try:
            # 只在首次從 B_ACCT_Q_TransferDetail 獲取 cookies
            if 'B_ACCT_Q_TransferDetail' in request.url:
                headers = request.headers
                auth_header = headers.get('authorization', '')
                
                if auth_header.startswith('Bearer '):
                    token = auth_header[7:]  # 移除 "Bearer " 前綴
                    log_manager.debug(f"獲取到初始 token: {token}", device=self.device_id)
                    
                    try:
                        post_data = json.loads(request.post_data)
                        customer_id = post_data.get('content', {}).get('customerId')
                        
                        if customer_id:
                            # 儲存 cookies 資訊
                            self.stored_cookies = {
                                "token": token,
                                "customerId": customer_id
                            }
                            # 首次更新到 API
                            if self.update_cookies(self.stored_cookies):
                                self.cookies_updated = True
                                self.last_cookie_update = time.time()
                                self.update_status("已獲取並更新初始 token")
                                log_manager.info("成功獲取並更新初始 token 和 customerId 到 API", device=self.device_id)
                                
                    except json.JSONDecodeError:
                        log_manager.error("解析請求數據失敗", device=self.device_id)
                        
        except Exception as e:
            log_manager.error(f"處理請求時發生錯誤: {str(e)}", device=self.device_id)

    def perform_periodic_tasks(self):
        """執行定時任務：重整頁面和更新 cookies"""
        try:
            # 重整頁面
            log_manager.debug("開始重整頁面", device=self.device_id)
            self.update_status("重整頁面以保持登入狀態...")
            self.page.reload()
            self.page.wait_for_load_state("networkidle")
            log_manager.debug("頁面重整完成", device=self.device_id)
            
            # 如果已有 stored_cookies，更新到 API
            if self.stored_cookies:
                if self.update_cookies(self.stored_cookies):
                    self.last_cookie_update = time.time()
                    self.update_status("已定時更新 token")
                    log_manager.info("成功定時更新 token 到 API", device=self.device_id)
                    return True
                else:
                    log_manager.error("定時更新 token 失敗", device=self.device_id)
                    return False
            else:
                log_manager.error("尚未獲取初始 token", device=self.device_id)
                return False
                
        except Exception as e:
            log_manager.error(f"執行定時任務時發生錯誤: {str(e)}", device=self.device_id)
            return False

    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待並獲取輸入欄位
            try:
                # 等待身分證字號標籤出現
                try:
                    id_label = self.page.get_by_text("身分證字號")
                    id_label.wait_for(state="visible", timeout=5000)
                except TimeoutError:
                    log_manager.debug("自動填入失敗：等待身分證字號標籤超時", device=self.device_id)
                    return False
                
                # 使用 ID 定位輸入欄位
                id_input = self.page.locator("#CustID")
                user_id_input = self.page.locator("#UserIdKeyin")
                password_input = self.page.locator("#PasswordKeyin")
                
                # 確保所有欄位都可見
                if not all([id_input.is_visible(), user_id_input.is_visible(), password_input.is_visible()]):
                    log_manager.debug("自動填入失敗：部分輸入欄位未顯示", device=self.device_id)
                    return False
            except Exception as e:
                log_manager.debug(f"自動填入失敗：等待輸入欄位超時 - {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])
                return True
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟國泰世華銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                self.page.goto("https://cathaybk.com.tw/mybank/")
                self.page.wait_for_load_state("networkidle")
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                self.update_status("登入成功，開始執行操作...")
                
                # 操作循環
                while self.is_running:
                    try:
                        current_time = time.time()
                        
                        # 檢查是否需要執行定時任務
                        if current_time - self.last_cookie_update >= self.cookie_update_interval:
                            self.perform_periodic_tasks()
                            
                        # 短暫休息以減少 CPU 使用
                        time.sleep(1)
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"執行操作時發生錯誤: {str(e)}",
                                "operation_error")
                            self.update_status("發生錯誤，等待重試...")
                            time.sleep(10)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    self.update_status("發生錯誤，等待重試...")
                    time.sleep(10)
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止國泰世華銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
