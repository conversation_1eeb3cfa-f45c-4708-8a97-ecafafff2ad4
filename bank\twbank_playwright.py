"""
台灣銀行操作模組 (Playwright 版本)
"""

import os
import time
import json
import threading
import queue
from datetime import datetime
from playwright.sync_api import expect
from .base_playwright import BankBase
from gui.utils.logger import log_manager

class TwBank(BankBase):
    """台灣銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, use_proxy: bool = False): # 新增 use_proxy 參數
        super().__init__(
            bank_code='004', # 使用正確的銀行代碼
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=False,
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.login_url = 'https://ebank.bot.com.tw/'
        self.keep_alive_thread = None
        self.cookies_data = None
        self.token = None
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        self.action_queue = queue.Queue()  # 用於線程間通信的隊列
        
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 前往登入頁面
                self.page.goto(self.login_url, wait_until="networkidle")
                self.update_status("等待登入...")
                
                # 等待登入成功
                if not self.wait_for_login():
                    continue
                    
                # 進入交易明細頁面
                if not self.navigate_to_transaction_details():
                    continue
                    
                # 主要監控循環
                while self.is_running:
                    try:
                        # 如果還沒有成功更新 cookies，嘗試重新更新
                        if not self.cookies_updated and self.cookies_data and self.token:
                            cookies_payload = {
                                "Cookie": self.cookies_data,
                                "__RequestVerificationToken": self.token
                            }
                            
                            if self.update_cookies(cookies_payload):
                                self.cookies_updated = True
                                self.last_cookie_update = time.time()
                                log_manager.debug("成功更新初始 cookies", device=self.device_id)
                                # 開始保持登入
                                self.start_keep_alive()
                            else:
                                log_manager.error("更新初始 cookies 失敗，稍後重試", device=self.device_id)
                                time.sleep(5)  # 等待一段時間後重試
                                continue
                                
                        # 檢查是否有需要執行的動作
                        try:
                            action = self.action_queue.get_nowait()
                            if action == "refresh":
                                # 使用更精確的選擇器定位重新計時按鈕
                                frame = self.page.locator("#MainFrame").content_frame
                                refresh_link = frame.locator("#BotTxnRegion > div.page-title-holder > div.page-info-box > div > span.login-countdown > a")
                                
                                # 等待按鈕出現並點擊
                                refresh_link.wait_for()
                                refresh_link.click()
                                log_manager.debug("已點擊重新計時按鈕", device=self.device_id)
                                
                                # 更新 cookies 到 API
                                if self.cookies_data and self.token:
                                    cookies_payload = {
                                        "Cookie": self.cookies_data,
                                        "__RequestVerificationToken": self.token
                                    }
                                    
                                    if self.update_cookies(cookies_payload):
                                        self.last_cookie_update = time.time()
                                        log_manager.debug("成功更新 cookies", device=self.device_id)
                                    else:
                                        log_manager.error("更新 cookies 失敗", device=self.device_id)
                                        
                        except queue.Empty:
                            pass
                            
                        time.sleep(1)  # 短暫休息以減少 CPU 使用
                        
                    except Exception as e:
                        if self.is_running:
                            self.log_with_screenshot('error',
                                f"監控過程中發生錯誤: {str(e)}",
                                "monitor_error")
                            time.sleep(5)
                            
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error',
                        f"執行過程中發生錯誤: {str(e)}",
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
            
    def wait_for_login(self):
        """等待登入完成"""
        try:
            # 等待重新計時連結出現，代表登入成功
            frame = self.page.locator("#MainFrame").content_frame
            refresh_link = frame.locator("#BotTxnRegion > div.page-title-holder > div.page-info-box > div > span.login-countdown > a")
            refresh_link.wait_for()
            self.update_status("登入成功")
            
            # 等待載入完成
            self.page.wait_for_load_state("networkidle")
            time.sleep(2)  # 額外等待確保頁面完全載入
            
            # 驗證帳號
            return self.verify_account()
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', f"等待登入時發生錯誤: {str(e)}", "login_wait_error")
            return False
            
    def verify_account(self):
        """驗證帳號 (包含重試機制)"""
        max_retries = 3
        retry_delay = 5
        account_verified = False
        main_frame = self.page.locator("#MainFrame").content_frame # 先獲取 frame

        for attempt in range(max_retries):
            if not self.is_running: return False # 每次重試前檢查

            log_manager.debug(f"開始驗證台灣銀行帳號 (嘗試 {attempt + 1}/{max_retries})...", device=self.device_id)
            try:
                # 等待載入完成
                processing_text = main_frame.locator("text=交易進行中，請稍候...")
                try:
                    if processing_text.is_visible(timeout=5000): # 檢查是否可見
                        processing_text.wait_for(state="hidden", timeout=15000) # 等待隱藏
                except Exception:
                    log_manager.debug("未找到 '交易進行中' 或等待隱藏超時，繼續執行", device=self.device_id)

                # 等待帳戶表格載入完成
                try:
                    main_frame.locator("table").wait_for(timeout=10000)
                    self.page.wait_for_load_state("networkidle", timeout=10000)
                    time.sleep(2) # 額外等待
                except Exception as table_wait_error:
                    log_manager.warning(f"第 {attempt + 1} 次嘗試等待表格或 networkidle 超時: {table_wait_error}", device=self.device_id)
                    # 等待失敗，進入重試邏輯
                    if attempt < max_retries - 1:
                        log_manager.info(f"等待 {retry_delay} 秒後重試...", device=self.device_id)
                        for _ in range(retry_delay):
                            if not self.is_running: return False
                            time.sleep(1)
                        continue # 繼續下一次重試
                    else:
                        self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍無法載入表格", "table_load_retries_failed")
                        self.stop()
                        return False

                # 尋找帳號
                account_cell_locator = main_frame.get_by_role("cell", name=self.bank_account)
                try:
                    account_cell_locator.wait_for(state="visible", timeout=5000) # 等待單元格可見
                    log_manager.info(f"帳號驗證成功: {self.bank_account} (嘗試 {attempt + 1})", device=self.device_id)
                    account_verified = True
                    break # 驗證成功，跳出重試循環
                except Exception as cell_wait_error: # 捕捉 TimeoutError 或其他錯誤
                    log_manager.warning(f"第 {attempt + 1} 次嘗試未找到帳號單元格或超時: {cell_wait_error}", device=self.device_id)
                    # 未找到，進入重試邏輯

                # --- 重試處理 ---
                if not account_verified and attempt < max_retries - 1:
                    log_manager.info(f"等待 {retry_delay} 秒後重試驗證帳號...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                elif not account_verified and attempt == max_retries - 1:
                    # 最後一次嘗試失敗
                    self.log_with_screenshot('error', f"帳號驗證失敗：重試 {max_retries} 次後仍未找到帳號 {self.bank_account}", "account_verify_retries_failed")
                    self.stop() # 驗證失敗，停止
                    return False

            except Exception as e:
                log_manager.error(f"驗證帳號時發生未預期錯誤 (嘗試 {attempt + 1}): {str(e)}", device=self.device_id)
                if attempt < max_retries - 1:
                    log_manager.info(f"發生錯誤，等待 {retry_delay} 秒後重試...", device=self.device_id)
                    for _ in range(retry_delay):
                        if not self.is_running: return False
                        time.sleep(1)
                else:
                    self.log_with_screenshot('error', f"驗證帳號時發生錯誤且已達最大重試次數: {str(e)}", "account_verify_error_final")
                    self.stop()
                    return False

        # --- 循環結束後 ---
        if account_verified:
            self.update_status("帳號驗證成功")
            return True
        else:
            # 理論上如果失敗會在循環內 return False
            log_manager.error("帳號驗證最終失敗 (已達最大重試次數)", device=self.device_id)
            if self.is_running: self.stop() # 確保停止
            return False
            
    def try_open_menu(self, function_cell):
        """嘗試展開選單"""
        attempt = 1
        
        while self.is_running:
            try:
                # 移動到按鈕上
                function_cell.hover()
                log_manager.debug(f"第 {attempt} 次嘗試移動到功能按鈕上", device=self.device_id)
                time.sleep(1)  # 等待選單展開
                
                # 檢查往來交易明細按鈕是否可見
                details_button = self.page.locator("#MainFrame").content_frame.get_by_role("button", name="往來交易明細")
                details_button.wait_for()
                log_manager.debug("往來交易明細按鈕已出現", device=self.device_id)
                details_button.click()
                log_manager.debug("已點擊往來交易明細按鈕", device=self.device_id)
                return True
                
            except Exception as e:
                log_manager.debug(f"第 {attempt} 次嘗試失敗: {str(e)}", device=self.device_id)
                
            attempt += 1
            time.sleep(1)  # 等待一下再試
                
        return False

    def try_download_excel(self, frame):
        """嘗試下載 Excel 檔案"""
        attempt = 1
        
        while self.is_running:
            try:
                # 等待另存檔案按鈕出現
                save_button = frame.get_by_role("button", name=" 另存檔案 ")
                save_button.wait_for()
                log_manager.debug("另存檔案按鈕已出現", device=self.device_id)
                
                # 移動到按鈕上並等待
                save_button.hover()
                log_manager.debug(f"第 {attempt} 次嘗試移動到另存檔案按鈕上", device=self.device_id)
                time.sleep(1)  # 等待選單展開
                
                # 檢查 Excel 檔按鈕是否可見
                excel_button = frame.locator("#TA0103_CsvDownload")
                excel_button.wait_for()
                log_manager.debug("Excel 檔按鈕已出現", device=self.device_id)
                
                # 監聽下載請求
                with self.page.expect_download() as download_info:
                    excel_button.click()
                    log_manager.debug("已點擊 Excel 檔按鈕", device=self.device_id)
                    
                # 獲取下載資訊
                download = download_info.value
                log_manager.debug(f"成功下載檔案: {download.suggested_filename}", device=self.device_id)
                
                # 從請求中提取 Cookie 和 Token
                request = download.url
                if request:
                    # 提取 Cookie
                    cookies = self.page.context.cookies()
                    self.cookies_data = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
                    
                    # 提取 Token
                    for cookie in cookies:
                        if cookie['name'] == '__RequestVerificationToken':
                            self.token = cookie['value']
                            break
                            
                    return True
                
            except Exception as e:
                log_manager.debug(f"第 {attempt} 次嘗試失敗: {str(e)}", device=self.device_id)
                
            attempt += 1
            time.sleep(1)  # 等待一下再試
                
        return False
            
    def navigate_to_transaction_details(self):
        """進入交易明細頁面"""
        try:
            # 等待頁面完全載入
            self.page.wait_for_load_state("networkidle")
            time.sleep(2)  # 額外等待確保頁面穩定
            
            # 找到帳號所在的行
            frame = self.page.locator("#MainFrame").content_frame
            account_row = frame.locator(f"tr:has(td:text('{self.bank_account}'))")
            account_row.wait_for()
            log_manager.debug("找到帳號所在行", device=self.device_id)
            
            # 在同一行中找到功能按鈕
            function_cell = account_row.locator("td.hidden-print")
            function_cell.wait_for()
            log_manager.debug("找到功能按鈕", device=self.device_id)
            
            # 嘗試展開選單並點擊往來交易明細按鈕
            if self.try_open_menu(function_cell):
                # 等待頁面載入完成
                self.page.wait_for_load_state("networkidle")
                time.sleep(2)  # 等待頁面穩定
                
                # 嘗試下載 Excel 檔案
                if self.try_download_excel(frame):
                    self.update_status("成功下載交易明細")
                    return True
                else:
                    log_manager.error("無法下載 Excel 檔案，但保持在當前頁面", device=self.device_id)
                    return True  # 仍然返回 True 以保持在當前頁面
                
            else:
                log_manager.error("無法展開功能選單", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', f"進入交易明細頁面時發生錯誤: {str(e)}", "navigation_error")
            return False
            
    def keep_alive(self):
        """保持登入狀態"""
        while self.is_running:
            try:
                current_time = time.time()
                # 檢查是否需要更新 cookies
                if current_time - self.last_cookie_update >= self.cookie_update_interval:
                    # 將重新計時的動作加入隊列
                    self.action_queue.put("refresh")
                    
                # 每秒檢查一次
                time.sleep(1)
                
            except Exception as e:
                if self.is_running:
                    log_manager.error(f"保持登入時發生錯誤: {str(e)}")
                break
                
    def start_keep_alive(self):
        """啟動保持登入執行緒"""
        self.keep_alive_thread = threading.Thread(target=self.keep_alive)
        self.keep_alive_thread.daemon = True
        self.keep_alive_thread.start()
        
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止台灣銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
