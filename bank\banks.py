"""
銀行資訊管理模組
提供銀行代號和名稱的映射關係
"""

from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class BankModuleInfo:
    """銀行模組資訊"""
    module: str        # 對應的模組名稱
    class_name: str    # 對應的類別名稱

@dataclass
class BankInfo:
    """銀行資訊類"""
    code: str          # 銀行代號
    name: str          # 銀行名稱
    modules: dict[int, BankModuleInfo] = None  # 不同帳戶類型對應的模組資訊 {account_type: BankModuleInfo}

    def __post_init__(self):
        if self.modules is None:
            self.modules = {}

# 支援的銀行列表
SUPPORTED_BANKS = [
    BankInfo("807", "永豐銀行", {
        1: BankModuleInfo("sinopac", "SinopacBank"),  # 個人帳戶
        2: BankModuleInfo("sinopac_corp", "SinopacCorpBank")  # 公司帳戶
    }),
    BankInfo("826", "樂天銀行", {
        1: BankModuleInfo("rakutenbank", "RakutenBank")
    }),
    BankInfo("808", "玉山銀行", {
        1: BankModuleInfo("yusan", "YusanBank")
    }),
    BankInfo("812", "台新銀行", {
        1: BankModuleInfo("taixin", "TaixinBank")
    }),
    BankInfo("013", "國泰世華銀行", {
        1: BankModuleInfo("cathaybk", "CathayBank")
    }),
    BankInfo("017", "兆豐銀行", {
        1: BankModuleInfo("megabank", "MegaBank")
    }),
    BankInfo("007", "第一銀行", {
        1: BankModuleInfo("firstbank", "FirstBank")
    }),
    BankInfo("824", "連線銀行", {
        1: BankModuleInfo("linebank", "LineBank")
    }),
    BankInfo("700", "郵局", {
        1: BankModuleInfo("post", "PostBank")
    }),
    BankInfo("823", "將來銀行", {
        1: BankModuleInfo("nextbank", "NextBank")
    }),
    BankInfo("008", "華南銀行", {
        1: BankModuleInfo("hncb", "HuaNanBank")
    }),
    BankInfo("012", "富邦銀行", {
        1: BankModuleInfo("fubon", "FubonBank")
    }),
]

class BankRegistry:
    """銀行註冊管理器"""
    
    def __init__(self):
        self._banks: Dict[str, BankInfo] = {
            bank.code: bank for bank in SUPPORTED_BANKS
        }
        
    def get_bank_by_code(self, code: str) -> Optional[BankInfo]:
        """根據銀行代號獲取銀行資訊"""
        return self._banks.get(code)
        
    def get_bank_by_name(self, name: str) -> Optional[BankInfo]:
        """根據銀行名稱獲取銀行資訊"""
        return next(
            (bank for bank in self._banks.values() if bank.name == name),
            None
        )
        
    def get_all_banks(self) -> List[BankInfo]:
        """獲取所有支援的銀行列表"""
        return list(self._banks.values())
        
    def get_bank_names(self) -> List[str]:
        """獲取所有支援的銀行名稱列表"""
        return [bank.name for bank in self._banks.values()]
        
    def get_bank_codes(self) -> List[str]:
        """獲取所有支援的銀行代號列表"""
        return list(self._banks.keys())
        
    def get_bank_module_info(self, code: str, account_type: int = 1) -> Optional[BankModuleInfo]:
        """根據銀行代號和帳戶類型獲取模組資訊"""
        bank = self._banks.get(code)
        if bank and account_type in bank.modules:
            return bank.modules[account_type]
        return None
        
    def get_supported_account_types(self, code: str) -> List[int]:
        """獲取銀行支援的帳戶類型列表"""
        bank = self._banks.get(code)
        if bank:
            return list(bank.modules.keys())
        return []

# 全局銀行註冊管理器實例
bank_registry = BankRegistry()
