"""
玉山銀行操作模組 (Playwright 版本)
實作玉山銀行特定的自動化操作
"""
import time
from playwright.sync_api import TimeoutError
from .base_playwright import BankBase
from gui.utils.logger import log_manager

class YusanBank(BankBase):
    """玉山銀行操作類"""
    
    def __init__(self, bank_account, device_id=None, device_data=None, is_mobile=True, use_proxy: bool = False): # 新增 is_mobile 和 use_proxy 參數
        super().__init__(
            bank_code="808",
            bank_account=bank_account,
            device_id=device_id,
            device_data=device_data,
            is_mobile=is_mobile,  # 支援手機版設定
            use_proxy=use_proxy # 傳遞代理設定
        )
        self.cookies_updated = False  # 追踪 cookies 是否已更新
        self.last_cookie_update = 0  # 上次更新 cookies 的時間
        self.cookie_update_interval = 60  # cookies 更新間隔（秒）
        
    def check_login_status(self):
        """檢查登入狀態"""
        if not self.is_running:
            return False
            
        try:
            # 等待並檢查"查看存款總覽"按鈕
            view_deposit = self.page.get_by_text("查看存款總覽")
            if view_deposit.is_visible():
                view_deposit.click()
                log_manager.debug("點擊查看存款總覽按鈕", device=self.device_id)
                
                # 等待頁面載入完成
                self.page.wait_for_load_state("networkidle")
                # 增加重試機制檢查帳號連結，最多重試 3 次，每次等待 5 秒
                max_retries = 3
                retry_delay = 5  # 每次重試間隔秒數 (修改為 5 秒)
                wait_timeout = 5000 # 等待元素可見的超時時間 (毫秒)

                for attempt in range(max_retries):
                    try:
                        account_link = self.page.get_by_role("link", name=self.bank_account)
                        # 等待元素可見，設定超時
                        account_link.wait_for(state="visible", timeout=wait_timeout)
                        log_manager.debug(f"帳號驗證成功: {self.bank_account} (嘗試 {attempt + 1}/{max_retries})", device=self.device_id)
                        return True
                    except TimeoutError:
                        log_manager.debug(f"第 {attempt + 1}/{max_retries} 次嘗試找不到帳號連結，等待 {retry_delay} 秒後重試...", device=self.device_id)
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay) # 重試前稍作等待
                        else:
                            # 最後一次嘗試失敗
                            self.log_with_screenshot('error',
                                f"重試 {max_retries} 次後仍找不到預期的帳號連結: {self.bank_account}",
                                "account_not_found_after_retries")
                            self.stop()
                            return False # 確保在 stop 後返回 False
                    except Exception as e:
                        # 處理其他可能的意外錯誤
                        self.log_with_screenshot('error',
                            f"檢查帳號連結時發生非預期錯誤 (嘗試 {attempt + 1}): {str(e)}",
                            "account_check_unexpected_error")
                        self.stop()
                        return False # 確保在 stop 後返回 False
                    
            return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"檢查登入狀態時發生錯誤: {str(e)}",
                    "login_check_error")
                self.stop()
            return False
            
    def check_and_handle_logout_notice(self):
        """檢查並處理登出提醒視窗"""
        if not self.is_running:
            return
            
        # 檢查是否有登出提醒
        logout_notice = self.page.get_by_label("即將被登出")
        if logout_notice.is_visible():
            # 點擊繼續使用按鈕
            self.page.get_by_text("繼續使用", exact=True).click()
            log_manager.debug("處理了登出提醒視窗", device=self.device_id)
            
    def get_and_update_cookies(self):
        """獲取並更新 cookies"""
        if not self.is_running:
            return
            
        try:
            current_time = time.time()
            # 檢查是否需要更新 cookies
            if current_time - self.last_cookie_update < self.cookie_update_interval:
                return
                
            # 獲取所有 cookies
            cookies = self.context.cookies()
            cookies_dict = {
                "JSESSIONID": next(
                    (cookie['value'] for cookie in cookies if cookie['name'] == 'JSESSIONID'),
                    None
                ),
                "ESB-W_Cookie": next(
                    (cookie['value'] for cookie in cookies if cookie['name'] == 'ESB-W_Cookie'),
                    None
                )
            }
            
            if all(cookies_dict.values()):
                if self.update_cookies(cookies_dict):
                    self.cookies_updated = True
                    self.last_cookie_update = current_time
                    self.update_status("已更新 cookies")
                else:
                    self.log_with_screenshot('warning', 
                        "更新 cookies 失敗", 
                        "update_cookies_failed")
            else:
                missing_cookies = [name for name, value in cookies_dict.items() if not value]
                self.log_with_screenshot('warning', 
                    f"無法獲取完整的 cookies: {', '.join(missing_cookies)}", 
                    "incomplete_cookies")
                    
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error', 
                    f"獲取 cookies 時發生錯誤: {str(e)}", 
                    "get_cookies_error")
                    
    def auto_fill_login_form(self):
        """自動填入登入表單"""
        if not self.is_running:
            log_manager.debug("自動填入失敗：程式未運行", device=self.device_id)
            return False
            
        if not self.should_auto_fill:
            log_manager.debug("自動填入已停用", device=self.device_id)
            return False
            
        if not self.device_data:
            log_manager.debug("自動填入失敗：無設備資料", device=self.device_id)
            return False
            
        try:
            # 等待並獲取輸入欄位
            try:
                # 等待身分證字號欄位出現
                try:
                    id_input = self.page.get_by_placeholder("身分證字號")
                    id_input.wait_for(state="visible", timeout=5000)
                except TimeoutError:
                    log_manager.debug("自動填入失敗：等待身分證字號欄位超時", device=self.device_id)
                    return False
                
                # 使用 placeholder 定位輸入欄位
                user_id_input = self.page.get_by_placeholder("使用者名稱")
                password_input = self.page.get_by_placeholder("使用者密碼")
                
                # 確保所有欄位都可見
                if not all([id_input.is_visible(), user_id_input.is_visible(), password_input.is_visible()]):
                    log_manager.debug("自動填入失敗：部分輸入欄位未顯示", device=self.device_id)
                    return False
            except Exception as e:
                log_manager.debug(f"自動填入失敗：等待輸入欄位超時 - {str(e)}", device=self.device_id)
                return False
                
            # 檢查所有必要的資料是否存在
            required_fields = {
                "身分證字號": self.device_data.get("bank_id_number"),
                "使用者代號": self.device_data.get("bank_user_id"),
                "使用者密碼": self.device_data.get("bank_user_password")
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                log_manager.debug(f"自動填入失敗：缺少必要資料 {', '.join(missing_fields)}", device=self.device_id)
                return False
                
            try:
                # 填入資料
                id_input.fill(self.device_data["bank_id_number"])
                user_id_input.fill(self.device_data["bank_user_id"])
                password_input.fill(self.device_data["bank_user_password"])
                return True
            except Exception as e:
                log_manager.debug(f"填入資料時發生錯誤: {str(e)}", device=self.device_id)
                return False
            
        except Exception as e:
            if self.is_running:
                self.log_with_screenshot('error',
                    f"自動填入登入資料時發生錯誤: {str(e)}",
                    "auto_fill_error")
            return False
            
    def start(self):
        """開始執行"""
        self.is_running = True
        self.setup_browser()
        self.update_status("正在開啟玉山銀行網頁...")
        
        # 檢查自動填入設定
        self.check_auto_fill_settings()
        
        while self.is_running:  # 主要循環，確保程式持續運行
            try:
                # 使用手機版網址
                self.page.goto("https://ebank.esunbank.com.tw/indexMobile.jsp", wait_until="networkidle")
                
                # 嘗試自動填入
                if self.should_auto_fill:
                    self.update_status("正在自動填入登入資料...")
                    if self.auto_fill_login_form():
                        log_manager.info("已完成自動填入", device=self.device_id)
                    else:
                        log_manager.warning("自動填入失敗", device=self.device_id)
                
                self.update_status("等待登入中...")
                
                # 等待登入成功（無超時限制）
                while self.is_running:
                    if self.check_login_status():
                        break
                    time.sleep(2)
                    
                if not self.is_running:
                    break
                    
                self.update_status("登入成功，開始監控...")
                
                # 主要監控循環
                while self.is_running:
                    # 檢查並處理登出提醒視窗
                    self.check_and_handle_logout_notice()
                    
                    # 更新 cookies
                    self.get_and_update_cookies()
                    
                    # 短暫休息以減少 CPU 使用
                    time.sleep(1)
                    
            except Exception as e:
                if self.is_running:
                    self.log_with_screenshot('error', 
                        f"執行過程中發生錯誤: {str(e)}", 
                        "general_error")
                    time.sleep(10)  # 等待10秒後重試
                    
        # 在主線程中關閉瀏覽器
        try:
            if self.browser:
                self.browser.close()
                log_manager.debug("瀏覽器已關閉", device=self.device_id)
            
            if self.playwright:
                self.playwright.stop()
                log_manager.debug("Playwright 已停止", device=self.device_id)
                
        except Exception as e:
            log_manager.error(f"關閉瀏覽器時發生錯誤: {str(e)}", device=self.device_id)
            
        # 清除引用
        self.page = None
        self.context = None
        self.browser = None
        self.playwright = None
                    
    def stop(self):
        """停止運行並釋放資源"""
        log_manager.debug("開始停止玉山銀行實例", device=self.device_id)
        self.is_running = False  # 這會讓主線程跳出循環並關閉瀏覽器
