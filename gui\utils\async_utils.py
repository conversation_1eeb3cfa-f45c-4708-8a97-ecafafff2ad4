import asyncio
import threading
import functools
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Callable, Any
from gui.utils.logger import log_manager

class AsyncUtils:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(AsyncUtils, cls).__new__(cls)
                cls._instance._initialize()
            return cls._instance
            
    def _initialize(self):
        """初始化異步工具"""
        self.executor = ThreadPoolExecutor(max_workers=10)
        
    def run_async(self, func: Callable, callback: Callable[[Any], None] = None, error_callback: Callable[[Exception], None] = None):
        """
        在後台線程中運行函數
        
        Args:
            func: 要運行的函數
            callback: 成功時的回調函數
            error_callback: 錯誤時的回調函數
        """
        def _async_run():
            try:
                result = func()
                if callback:
                    # 在主線程中執行回調
                    self._safe_callback(callback, result)
            except Exception as e:
                log_manager.exception(f"異步執行時發生錯誤: {str(e)}")
                if error_callback:
                    # 在主線程中執行錯誤回調
                    self._safe_callback(error_callback, e)
                        
        # 在線程池中執行
        self.executor.submit(_async_run)
        
    def _safe_callback(self, callback: Callable, *args):
        """安全地執行回調函數
        
        Args:
            callback: 回調函數
            *args: 回調函數的參數
        """
        try:
            # 檢查是否是綁定方法（類的方法）
            if hasattr(callback, '__self__') and hasattr(callback.__self__, 'after'):
                # 使用 after 方法在主線程中執行
                callback.__self__.after(0, lambda: callback(*args))
            else:
                # 直接執行回調
                callback(*args)
        except Exception as e:
            log_manager.exception(f"執行回調時發生錯誤: {str(e)}")
        
    def cleanup(self):
        """清理資源"""
        self.executor.shutdown(wait=False)

# 創建全局異步工具實例
async_utils = AsyncUtils()
